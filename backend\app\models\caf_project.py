"""
CAF项目数据模型
支持Excel中的所有31个字段
"""

import os
from typing import Dict, List, Any, Optional
from datetime import datetime

from .base import BaseModel


class CAFProject(BaseModel):
    """CAF项目模型类"""
    
    def __init__(self, **kwargs):
        """初始化CAF项目实例"""
        super().__init__(**kwargs)
        
        # 基础信息字段
        self.sequence_number = kwargs.get('sequence_number', 0)
        self.project_name = kwargs.get('project_name', '')
        self.project_code = kwargs.get('project_code', '')
        self.caf_code = kwargs.get('caf_code', '')
        self.tmt_code = kwargs.get('tmt_code', '')
        self.chinese_name = kwargs.get('chinese_name', '')
        self.english_name = kwargs.get('english_name', '')
        self.technical_lead = kwargs.get('technical_lead', '')
        self.technical_manager = kwargs.get('technical_manager', '')
        self.stage = kwargs.get('stage', '')
        self.project_start_date = kwargs.get('project_start_date', None)
        self.fatigue_test_conditions = kwargs.get('fatigue_test_conditions', '')
        
        # 状态信息字段
        self.cbc_status = kwargs.get('cbc_status', '')
        self.drawing_code_version = kwargs.get('drawing_code_version', '')
        self.drawing_status = kwargs.get('drawing_status', '')
        self.ttp_code_version = kwargs.get('ttp_code_version', '')
        self.ttp_status = kwargs.get('ttp_status', '')
        self.rtp_code_version = kwargs.get('rtp_code_version', '')
        self.rtp_status = kwargs.get('rtp_status', '')
        self.ttr_code_version = kwargs.get('ttr_code_version', '')
        self.ttr_status = kwargs.get('ttr_status', '')
        
        # 时间和进度信息
        self.internal_drawing_release_time = kwargs.get('internal_drawing_release_time', '')
        self.fai_date = kwargs.get('fai_date', None)
        self.description = kwargs.get('description', '')
        
        # 会议和讨论记录
        self.caf_onsite_discussion_1002 = kwargs.get('caf_onsite_discussion_1002', '')
        self.project_progress_1 = kwargs.get('project_progress_1', '')
        self.project_progress_2 = kwargs.get('project_progress_2', '')
        self.project_progress_3 = kwargs.get('project_progress_3', '')
        self.caf_issues_discussion_0319 = kwargs.get('caf_issues_discussion_0319', '')
        self.caf_meeting_issues_0414 = kwargs.get('caf_meeting_issues_0414', '')
        self.caf_meeting_issues_0616 = kwargs.get('caf_meeting_issues_0616', '')
        
        # 计算字段
        self.difficulty_level = self._calculate_difficulty_level()
        self.progress_percentage = self._calculate_progress_percentage()
        self.completion_date = self._extract_completion_date()
    
    @classmethod
    def get_data_file(cls) -> str:
        """获取数据文件路径"""
        return os.path.join(cls.get_data_dir(), 'caf_projects.json')
    
    def to_core_dict(self) -> Dict[str, Any]:
        """转换为核心字段字典（用于主表格显示）"""
        return {
            'id': self.id,
            'sequence_number': self.sequence_number,
            'project_name': self.project_name,
            'project_code': self.project_code,
            'tmt_code': self.tmt_code,
            'technical_lead': self.technical_lead,
            'difficulty_level': self.difficulty_level,
            'progress_percentage': self.progress_percentage,
            'stage': self.stage,
            'project_start_date': self.project_start_date,
            'completion_date': self.completion_date,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    def to_detail_dict(self) -> Dict[str, Any]:
        """转换为详细字典（用于详情模态框显示）"""
        return {
            # 核心信息概览
            'core_info': {
                'project_name': self.project_name,
                'project_code': self.project_code,
                'tmt_code': self.tmt_code,
                'technical_lead': self.technical_lead,
                'stage': self.stage,
                'project_start_date': self.project_start_date
            },
            
            # 完整项目数据
            'complete_data': {
                'sequence_number': self.sequence_number,
                'project_start_date': self.project_start_date,
                'cbc_status': self.cbc_status,
                'drawing_code_version': self.drawing_code_version,
                'drawing_status': self.drawing_status,
                'ttp_code_version': self.ttp_code_version,
                'ttp_status': self.ttp_status,
                'rtp_code_version': self.rtp_code_version,
                'rtp_status': self.rtp_status,
                'ttr_code_version': self.ttr_code_version,
                'ttr_status': self.ttr_status,
                'internal_drawing_release_time': self.internal_drawing_release_time,
                'fai_date': self.fai_date,
                'description': self.description,
                'caf_onsite_discussion_1002': self.caf_onsite_discussion_1002,
                'project_progress_1': self.project_progress_1,
                'project_progress_2': self.project_progress_2,
                'project_progress_3': self.project_progress_3,
                'caf_issues_discussion_0319': self.caf_issues_discussion_0319,
                'caf_meeting_issues_0414': self.caf_meeting_issues_0414,
                'caf_meeting_issues_0616': self.caf_meeting_issues_0616
            },
            
            # 系统信息
            'system_info': {
                'id': self.id,
                'created_at': self.created_at,
                'updated_at': self.updated_at
            }
        }
    
    def _calculate_difficulty_level(self) -> str:
        """根据项目信息计算技术难度"""
        # 基于疲劳试验条件、阶段等信息推算难度
        if self.fatigue_test_conditions:
            conditions = str(self.fatigue_test_conditions).lower()
            if '1000万次' in conditions or '高频' in conditions:
                return '高'
            elif '300万次' in conditions or '中频' in conditions:
                return '中'
            else:
                return '低'
        
        # 基于阶段判断
        stage = str(self.stage).lower()
        if '首检' in stage or '试验' in stage:
            return '高'
        elif '协议' in stage:
            return '中'
        else:
            return '待评估'
    
    def _calculate_progress_percentage(self) -> int:
        """根据各状态计算进度百分比"""
        progress_indicators = [
            self.cbc_status,
            self.drawing_status,
            self.ttp_status,
            self.rtp_status,
            self.ttr_status
        ]
        
        completed_count = sum([1 for status in progress_indicators 
                              if status and ('批准' in str(status) or '完成' in str(status) or 'approved' in str(status).lower())])
        
        total_count = len([status for status in progress_indicators if status])
        
        if total_count == 0:
            return 0
        
        return int((completed_count / total_count) * 100)
    
    def _extract_completion_date(self) -> Optional[str]:
        """从状态信息中提取完成时间"""
        # 检查TTR状态，通常是最后完成的
        if self.ttr_status and ('批准' in str(self.ttr_status) or 'approved' in str(self.ttr_status).lower()):
            return self.ttr_status
        
        # 检查其他状态
        for status in [self.rtp_status, self.ttp_status, self.drawing_status]:
            if status and ('批准' in str(status) or 'approved' in str(status).lower()):
                return status
        
        return None
    
    @classmethod
    def find_all(cls, filters: Dict[str, Any] = None) -> List['CAFProject']:
        """查找所有CAF项目"""
        data = cls._load_data()
        projects = [cls.from_dict(item) for item in data]
        
        if filters:
            filtered_projects = []
            for project in projects:
                match = True
                
                # 按技术负责人筛选
                if filters.get('technical_lead') and project.technical_lead != filters['technical_lead']:
                    match = False
                
                # 按阶段筛选
                if filters.get('stage') and project.stage != filters['stage']:
                    match = False
                
                # 按项目名称筛选
                if filters.get('project_name') and filters['project_name'].lower() not in project.project_name.lower():
                    match = False
                
                if match:
                    filtered_projects.append(project)
            
            return filtered_projects
        
        return projects
    
    @classmethod
    def find_by_technical_lead(cls, technical_lead: str) -> List['CAFProject']:
        """根据技术负责人查找项目"""
        return cls.find_all({'technical_lead': technical_lead})
    
    @classmethod
    def find_by_stage(cls, stage: str) -> List['CAFProject']:
        """根据阶段查找项目"""
        return cls.find_all({'stage': stage})
    
    @classmethod
    def get_all_technical_leads(cls) -> List[str]:
        """获取所有技术负责人列表"""
        projects = cls.find_all()
        leads = list(set([project.technical_lead for project in projects if project.technical_lead]))
        return sorted(leads)
    
    @classmethod
    def get_all_stages(cls) -> List[str]:
        """获取所有阶段列表"""
        projects = cls.find_all()
        stages = list(set([project.stage for project in projects if project.stage]))
        return sorted(stages)

    def delete(self) -> None:
        """删除CAF项目"""
        data = self._load_data()
        data = [item for item in data if item.get('id') != self.id]
        self._save_data(data)
