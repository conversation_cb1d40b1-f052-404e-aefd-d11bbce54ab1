#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入CAF项目数据脚本
"""

import sys
import os

# 添加backend目录到Python路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

# 设置Flask应用上下文
os.environ['FLASK_ENV'] = 'development'

# 创建Flask应用实例
from backend.app import create_app
app = create_app()

with app.app_context():
    from backend.app.services.excel_service import ExcelService
    from backend.app.models.caf_project import CAFProject

def import_caf_data():
    """导入CAF项目数据"""
    file_path = '00 CAF项目进展情况.xlsx'

    if not os.path.exists(file_path):
        print(f'Excel文件不存在: {file_path}')
        return

    print('开始导入CAF项目数据...')

    try:
        # 使用ExcelService导入数据
        result = ExcelService.import_caf_project_data(file_path)
        
        if result['success']:
            print(f"Excel解析成功:")
            print(f"- 成功解析: {result['imported_count']} 个项目")
            print(f"- 失败行数: {result['failed_count']} 行")
            print(f"- 工作表: {result['sheet_name']}")
            
            if result['failed_rows']:
                print("\n失败的行:")
                for failed_row in result['failed_rows']:
                    print(f"  行 {failed_row['row']}: {failed_row['error']}")
            
            # 保存到数据库
            print('\n开始保存到数据库...')
            saved_count = 0
            failed_save_count = 0
            
            for project_data in result['projects']:
                try:
                    # 检查是否已存在相同序号的项目
                    existing_projects = CAFProject.find_all()
                    existing_project = None
                    for p in existing_projects:
                        if p.sequence_number == project_data.get('sequence_number'):
                            existing_project = p
                            break
                    
                    if existing_project:
                        # 更新现有项目
                        for key, value in project_data.items():
                            if hasattr(existing_project, key):
                                setattr(existing_project, key, value)
                        existing_project.save()
                        print(f"更新项目: {project_data.get('project_name', 'Unknown')}")
                    else:
                        # 创建新项目
                        project = CAFProject(**project_data)
                        project.save()
                        print(f"创建项目: {project_data.get('project_name', 'Unknown')}")
                    
                    saved_count += 1
                    
                except Exception as e:
                    print(f"保存项目失败: {project_data.get('project_name', 'Unknown')} - {e}")
                    failed_save_count += 1
            
            print(f'\n导入完成:')
            print(f"- 成功保存: {saved_count} 个项目")
            print(f"- 保存失败: {failed_save_count} 个项目")
            
            # 显示导入的项目列表
            print('\n导入的项目列表:')
            all_projects = CAFProject.find_all()
            for i, project in enumerate(all_projects[:10], 1):  # 只显示前10个
                print(f"{i:2d}. {project.project_name} - {project.technical_lead} - {project.stage}")
            
            if len(all_projects) > 10:
                print(f"... 还有 {len(all_projects) - 10} 个项目")
            
        else:
            print(f"导入失败: {result['message']}")
            
    except Exception as e:
        print(f'导入过程中出错: {e}')

if __name__ == '__main__':
    with app.app_context():
        import_caf_data()
