#!/usr/bin/env python3
"""
智能任务协调系统后端启动文件
"""

import os
import sys
import traceback

def main():
    """主函数"""
    try:
        print("正在启动后端服务器...")

        # 设置环境变量
        os.environ.setdefault('FLASK_ENV', 'development')

        # 创建Flask应用
        print("正在创建Flask应用...")
        from app import create_app
        app = create_app()
        print("Flask应用创建成功！")

        # 启动开发服务器
        print("正在启动开发服务器...")
        app.run(
            host='0.0.0.0',
            port=5001,
            debug=True,
            threaded=True,
            use_reloader=False  # 禁用重载器避免重复初始化
        )

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
