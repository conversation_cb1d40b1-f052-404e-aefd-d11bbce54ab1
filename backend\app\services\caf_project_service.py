"""
CAF项目管理服务
提供CAF项目的增删改查和业务逻辑处理
"""

from typing import Dict, List, Any, Optional
from datetime import datetime

from ..models import CAFProject
from ..utils import paginate_list


class CAFProjectService:
    """CAF项目管理服务类"""
    
    @staticmethod
    def get_projects_list(page: int = 1, per_page: int = 20, 
                         filters: Dict[str, Any] = None,
                         sort_by: str = 'sequence_number',
                         sort_order: str = 'asc') -> Dict[str, Any]:
        """获取CAF项目列表"""
        try:
            # 获取所有项目
            all_projects = CAFProject.find_all(filters)
            
            # 排序
            reverse = sort_order.lower() == 'desc'
            if sort_by == 'sequence_number':
                all_projects.sort(key=lambda x: x.sequence_number or 0, reverse=reverse)
            elif sort_by == 'project_name':
                all_projects.sort(key=lambda x: x.project_name or '', reverse=reverse)
            elif sort_by == 'technical_lead':
                all_projects.sort(key=lambda x: x.technical_lead or '', reverse=reverse)
            elif sort_by == 'stage':
                all_projects.sort(key=lambda x: x.stage or '', reverse=reverse)
            elif sort_by == 'updated_at':
                all_projects.sort(key=lambda x: x.updated_at or '', reverse=reverse)
            
            # 分页
            paginated_result = paginate_list(all_projects, page, per_page)
            
            # 转换为字典格式
            projects_data = [project.to_core_dict() for project in paginated_result['items']]
            
            return {
                'success': True,
                'projects': projects_data,
                'pagination': paginated_result['pagination']
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'获取项目列表失败: {str(e)}',
                'error_code': 'GET_PROJECTS_ERROR'
            }
    
    @staticmethod
    def get_project_detail(project_id: str) -> Dict[str, Any]:
        """获取CAF项目详情"""
        try:
            project = CAFProject.find_by_id(project_id)
            if not project:
                return {
                    'success': False,
                    'message': '项目不存在',
                    'error_code': 'PROJECT_NOT_FOUND'
                }
            
            return {
                'success': True,
                'project': project.to_detail_dict()
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'获取项目详情失败: {str(e)}',
                'error_code': 'GET_PROJECT_DETAIL_ERROR'
            }
    
    @staticmethod
    def create_project(project_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建CAF项目"""
        try:
            # 验证必填字段
            required_fields = ['project_name', 'technical_lead']
            for field in required_fields:
                if not project_data.get(field):
                    return {
                        'success': False,
                        'message': f'缺少必填字段: {field}',
                        'error_code': 'MISSING_REQUIRED_FIELD'
                    }
            
            # 检查序号是否重复
            if project_data.get('sequence_number'):
                existing_project = CAFProject.find_all({'sequence_number': project_data['sequence_number']})
                if existing_project:
                    return {
                        'success': False,
                        'message': '序号已存在',
                        'error_code': 'SEQUENCE_NUMBER_EXISTS'
                    }
            
            # 创建项目实例
            project = CAFProject(**project_data)
            project.save()
            
            return {
                'success': True,
                'message': '项目创建成功',
                'project': project.to_core_dict()
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'创建项目失败: {str(e)}',
                'error_code': 'CREATE_PROJECT_ERROR'
            }
    
    @staticmethod
    def update_project(project_id: str, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新CAF项目"""
        try:
            project = CAFProject.find_by_id(project_id)
            if not project:
                return {
                    'success': False,
                    'message': '项目不存在',
                    'error_code': 'PROJECT_NOT_FOUND'
                }
            
            # 检查序号是否重复（排除当前项目）
            if project_data.get('sequence_number') and project_data['sequence_number'] != project.sequence_number:
                existing_project = CAFProject.find_all({'sequence_number': project_data['sequence_number']})
                if existing_project:
                    return {
                        'success': False,
                        'message': '序号已存在',
                        'error_code': 'SEQUENCE_NUMBER_EXISTS'
                    }
            
            # 更新项目属性
            for key, value in project_data.items():
                if hasattr(project, key):
                    setattr(project, key, value)
            
            # 重新计算计算字段
            project.difficulty_level = project._calculate_difficulty_level()
            project.progress_percentage = project._calculate_progress_percentage()
            project.completion_date = project._extract_completion_date()
            
            project.save()
            
            return {
                'success': True,
                'message': '项目更新成功',
                'project': project.to_core_dict()
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'更新项目失败: {str(e)}',
                'error_code': 'UPDATE_PROJECT_ERROR'
            }
    
    @staticmethod
    def delete_project(project_id: str) -> Dict[str, Any]:
        """删除CAF项目"""
        try:
            project = CAFProject.find_by_id(project_id)
            if not project:
                return {
                    'success': False,
                    'message': '项目不存在',
                    'error_code': 'PROJECT_NOT_FOUND'
                }
            
            project.delete()
            
            return {
                'success': True,
                'message': '项目删除成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'删除项目失败: {str(e)}',
                'error_code': 'DELETE_PROJECT_ERROR'
            }
    
    @staticmethod
    def get_filter_options() -> Dict[str, Any]:
        """获取筛选选项"""
        try:
            return {
                'success': True,
                'options': {
                    'technical_leads': CAFProject.get_all_technical_leads(),
                    'stages': CAFProject.get_all_stages(),
                    'difficulty_levels': ['高', '中', '低', '待评估']
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'获取筛选选项失败: {str(e)}',
                'error_code': 'GET_FILTER_OPTIONS_ERROR'
            }
    
    @staticmethod
    def get_statistics() -> Dict[str, Any]:
        """获取项目统计信息"""
        try:
            all_projects = CAFProject.find_all()
            
            # 按阶段统计
            stage_stats = {}
            for project in all_projects:
                stage = project.stage or '未知'
                stage_stats[stage] = stage_stats.get(stage, 0) + 1
            
            # 按技术负责人统计
            lead_stats = {}
            for project in all_projects:
                lead = project.technical_lead or '未分配'
                lead_stats[lead] = lead_stats.get(lead, 0) + 1
            
            # 按难度统计
            difficulty_stats = {}
            for project in all_projects:
                difficulty = project.difficulty_level or '待评估'
                difficulty_stats[difficulty] = difficulty_stats.get(difficulty, 0) + 1
            
            # 进度统计
            progress_ranges = {'0-25%': 0, '26-50%': 0, '51-75%': 0, '76-100%': 0}
            for project in all_projects:
                progress = project.progress_percentage
                if progress <= 25:
                    progress_ranges['0-25%'] += 1
                elif progress <= 50:
                    progress_ranges['26-50%'] += 1
                elif progress <= 75:
                    progress_ranges['51-75%'] += 1
                else:
                    progress_ranges['76-100%'] += 1
            
            return {
                'success': True,
                'statistics': {
                    'total_projects': len(all_projects),
                    'stage_distribution': stage_stats,
                    'lead_distribution': lead_stats,
                    'difficulty_distribution': difficulty_stats,
                    'progress_distribution': progress_ranges
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'获取统计信息失败: {str(e)}',
                'error_code': 'GET_STATISTICS_ERROR'
            }
