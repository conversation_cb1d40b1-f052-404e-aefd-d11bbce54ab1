!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):"object"==typeof exports?exports.Slider=t(require("react")):e.<PERSON>lider=t(e.React)}(window,(function(e){return n=[function(e,t,n){var r=n(2);function i(e,t){var n,r=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)),r}e.exports=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e},e.exports.__esModule=!0,e.exports.default=e.exports},function(t,n){t.exports=e},function(e,t,n){var r=n(13);e.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(13);function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,r(i.key),i)}}e.exports=function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(18);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r;
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=o(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t,r="";for(t in e)n.call(e,t)&&e[t]&&(r=o(r,t));return r}(r)))}return e}function o(e,t){return t?e?e+" "+t:e+t:e}e.exports?e.exports=i.default=i:void 0!==(r=function(){return i}.apply(t,[]))&&(e.exports=r)}()},function(e,t,n){var r=n(11).default,i=n(17);e.exports=function(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return i(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return e.exports=function(){return!!t},e.exports.__esModule=!0,(e.exports.default=e.exports)()},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(){return e.exports=n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,n.apply(this,arguments)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){function r(e){var t="",n=Object.keys(e);return n.forEach((function(r,s){var a=e[r];r=i(r),o(r)&&"number"==typeof a&&(a+="px"),t+=!0===a?r:!1===a?"not "+r:"("+r+": "+a+")",s<n.length-1&&(t+=" and ")})),t}var i=n(21),o=function(e){return/[height|width]$/.test(e)};e.exports=function(e){var t="";return"string"==typeof e?e:e instanceof Array?(e.forEach((function(n,i){t+=r(n),i<e.length-1&&(t+=", ")})),t):r(e)}},function(e,t,n){var r=n(11).default,i=n(16);e.exports=function(e){return e=i(e,"string"),"symbol"==r(e)?e:e+""},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(19);e.exports=function(e,t){if(null==e)return{};var n,i=r(e,t);if(Object.getOwnPropertySymbols)for(var o=Object.getOwnPropertySymbols(e),s=0;s<o.length;s++)n=o[s],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n]);return i},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){"use strict";(function(e){var n="undefined"!=typeof Map?Map:(Object.defineProperty(r.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),r.prototype.get=function(e){return e=i(this.__entries__,e),(e=this.__entries__[e])&&e[1]},r.prototype.set=function(e,t){var n=i(this.__entries__,e);~n?this.__entries__[n][1]=t:this.__entries__.push([e,t])},r.prototype.delete=function(e){var t=this.__entries__;~(e=i(t,e))&&t.splice(e,1)},r.prototype.has=function(e){return!!~i(this.__entries__,e)},r.prototype.clear=function(){this.__entries__.splice(0)},r.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var i=r[n];e.call(t,i[1],i[0])}},r);function r(){this.__entries__=[]}function i(e,t){var n=-1;return e.some((function(e,r){return e[0]===t&&(n=r,!0)})),n}var o="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,s=void 0!==e&&e.Math===Math?e:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),a="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(s):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)},l=["top","right","bottom","left","width","height","size","weight"],c="undefined"!=typeof MutationObserver,u=(d.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},d.prototype.removeObserver=function(e){var t=this.observers_;~(e=t.indexOf(e))&&t.splice(e,1),!t.length&&this.connected_&&this.disconnect_()},d.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},d.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),0<e.length},d.prototype.connect_=function(){o&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),c?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},d.prototype.disconnect_=function(){o&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},d.prototype.onTransitionEnd_=function(e){var t=void 0===(e=e.propertyName)?"":e;l.some((function(e){return!!~t.indexOf(e)}))&&this.refresh()},d.getInstance=function(){return this.instance_||(this.instance_=new d),this.instance_},d.instance_=null,d);function d(){function e(){o&&(o=!1,r()),s&&n()}function t(){a(e)}function n(){var e=Date.now();if(o){if(e-l<2)return;s=!0}else s=!(o=!0),setTimeout(t,i);l=e}var r,i,o,s,l;this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=(r=this.refresh.bind(this),s=o=!(i=20),l=0,n)}var p=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];Object.defineProperty(e,i,{value:t[i],enumerable:!1,writable:!1,configurable:!0})}return e},f=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||s},h=m(0,0,0,0);function v(e){return parseFloat(e)||0}function y(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){return t+v(e["border-"+n+"-width"])}),0)}var g="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof f(e).SVGGraphicsElement}:function(e){return e instanceof f(e).SVGElement&&"function"==typeof e.getBBox};function S(e){var t;return o?g(e)?m(0,0,(t=(t=e).getBBox()).width,t.height):function(e){var t,n,r,i,o,s,a=e.clientWidth,l=e.clientHeight;return a||l?(n=(t=function(e){for(var t={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var i=r[n],o=e["padding-"+i];t[i]=v(o)}return t}(s=f(e).getComputedStyle(e))).left+t.right,r=t.top+t.bottom,i=v(s.width),o=v(s.height),"border-box"===s.boxSizing&&(Math.round(i+n)!==a&&(i-=y(s,"left","right")+n),Math.round(o+r)!==l)&&(o-=y(s,"top","bottom")+r),e!==f(e).document.documentElement&&(s=Math.round(i+n)-a,e=Math.round(o+r)-l,1!==Math.abs(s)&&(i-=s),1!==Math.abs(e))&&(o-=e),m(t.left,t.top,i,o)):h}(e):h}function m(e,t,n,r){return{x:e,y:t,width:n,height:r}}w.prototype.isActive=function(){var e=S(this.target);return(this.contentRect_=e).width!==this.broadcastWidth||e.height!==this.broadcastHeight},w.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e};var b=w;function w(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=m(0,0,0,0),this.target=e}var k=function(e,t){n=(t=t).x,r=t.y,o=t.width,t=t.height,i="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,i=Object.create(i.prototype),p(i,{x:n,y:r,width:o,height:t,top:r,right:n+o,bottom:t+r,left:n});var n,r,i,o=i;p(this,{target:e,contentRect:o})},x=(_.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof f(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new b(e)),this.controller_.addObserver(this),this.controller_.refresh())}},_.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof f(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},_.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},_.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},_.prototype.broadcastActive=function(){var e,t;this.hasActive()&&(e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new k(e.target,e.broadcastRect())})),this.callback_.call(e,t,e),this.clearActive())},_.prototype.clearActive=function(){this.activeObservations_.splice(0)},_.prototype.hasActive=function(){return 0<this.activeObservations_.length},_);function _(e,t,r){if(this.activeObservations_=[],this.observations_=new n,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=r}var T=new("undefined"!=typeof WeakMap?WeakMap:n),O=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=u.getInstance();t=new x(t,n,this);T.set(this,t)};["observe","unobserve","disconnect"].forEach((function(e){O.prototype[e]=function(){var t;return(t=T.get(this))[e].apply(t,arguments)}})),e=void 0!==s.ResizeObserver?s.ResizeObserver:O;t.a=e}).call(this,n(20))},function(e,t,n){var r=n(11).default;e.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);if(n=n.call(e,t||"default"),"object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t,r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){if(null==e)return{};for(var n,r={},i=Object.keys(e),o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||(r[n]=e[n]);return r},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){var n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t){e.exports=function(e){return e.replace(/[A-Z]/g,(function(e){return"-"+e.toLowerCase()})).toLowerCase()}},function(e,t,n){"use strict";n.r(t);var r=n(10),i=n.n(r),o=(r=n(0),n.n(r)),s=(r=n(4),n.n(r)),a=(r=n(5),n.n(r)),l=(r=n(8),n.n(r)),c=(r=n(9),n.n(r)),u=(r=n(3),n.n(r)),d=(r=n(6),n.n(r)),p=(r=n(2),n.n(r)),f=(r=n(1),n.n(r)),h=(r=n(11),n.n(r)),v=(r=n(14),n.n(r)),y={animating:!1,autoplaying:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,dragging:!1,edgeDragged:!1,initialized:!1,lazyLoadedList:[],listHeight:null,listWidth:null,scrolling:!1,slideCount:null,slideHeight:null,slideWidth:null,swipeLeft:null,swiped:!1,swiping:!1,touchObject:{startX:0,startY:0,curX:0,curY:0},trackStyle:{},trackWidth:0,targetSlide:0};r=n(7);var g=n.n(r),S={accessibility:!0,adaptiveHeight:!1,afterChange:null,appendDots:function(e){return f.a.createElement("ul",{style:{display:"block"}},e)},arrows:!0,autoplay:!1,autoplaySpeed:3e3,beforeChange:null,centerMode:!1,centerPadding:"50px",className:"",cssEase:"ease",customPaging:function(e){return f.a.createElement("button",null,e+1)},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,infinite:!0,initialSlide:0,lazyLoad:null,nextArrow:null,onEdge:null,onInit:null,onLazyLoadError:null,onReInit:null,pauseOnDotsHover:!1,pauseOnFocus:!1,pauseOnHover:!0,prevArrow:null,responsive:null,rows:1,rtl:!1,slide:"div",slidesPerRow:1,slidesToScroll:1,slidesToShow:1,speed:500,swipe:!0,swipeEvent:null,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,waitForAnimate:!0,asNavFor:null};function m(e,t,n){return Math.max(t,Math.min(e,n))}function b(e,t){var n={};return t.forEach((function(t){return n[t]=e[t]})),n}function w(e){return e.unslick||!e.infinite?0:e.slideCount}function k(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}var x=function(e){["onTouchStart","onTouchMove","onWheel"].includes(e._reactName)||e.preventDefault()},_=function(e){for(var t=[],n=T(e),r=O(e),i=n;i<r;i++)e.lazyLoadedList.indexOf(i)<0&&t.push(i);return t},T=function(e){return e.currentSlide-M(e)},O=function(e){return e.currentSlide+E(e)},M=function(e){return e.centerMode?Math.floor(e.slidesToShow/2)+(0<parseInt(e.centerPadding)?1:0):0},E=function(e){return e.centerMode?Math.floor((e.slidesToShow-1)/2)+1+(0<parseInt(e.centerPadding)?1:0):e.slidesToShow},L=function(e){return e&&e.offsetWidth||0},C=function(e){return e&&e.offsetHeight||0},z=function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=e.startX-e.curX;e=e.startY-e.curY,e=Math.atan2(e,n);return(n=(n=Math.round(180*e/Math.PI))<0?360-Math.abs(n):n)<=45&&0<=n||n<=360&&315<=n?"left":135<=n&&n<=225?"right":!0===t?35<=n&&n<=135?"up":"down":"vertical"},P=function(e){var t=!0;return e.infinite||(e.centerMode&&e.currentSlide>=e.slideCount-1||e.slideCount<=e.slidesToShow||e.currentSlide>=e.slideCount-e.slidesToShow)&&(t=!1),t},R=function(e,t){var n=function(e){for(var t=e.infinite?2*e.slideCount:e.slideCount,n=e.infinite?-1*e.slidesToShow:0,r=e.infinite?-1*e.slidesToShow:0,i=[];n<t;)i.push(n),n=r+e.slidesToScroll,r+=Math.min(e.slidesToScroll,e.slidesToShow);return i}(e),r=0;if(t>n[n.length-1])t=n[n.length-1];else for(var i in n){if(t<n[i]){t=r;break}r=n[i]}return t},W=function(e){var t,n,r=e.centerMode?e.slideWidth*Math.floor(e.slidesToShow/2):0;return e.swipeToSlide?(n=(n=e.listRef).querySelectorAll&&n.querySelectorAll(".slick-slide")||[],Array.from(n).every((function(n){if(e.vertical){if(n.offsetTop+C(n)/2>-1*e.swipeLeft)return t=n,!1}else if(n.offsetLeft-r+L(n)/2>-1*e.swipeLeft)return t=n,!1;return!0})),t?(n=!0===e.rtl?e.slideCount-e.currentSlide:e.currentSlide,Math.abs(t.dataset.index-n)||1):0):e.slidesToScroll},H=function(e,t){return t.reduce((function(t,n){return t&&e.hasOwnProperty(n)}),!0)?null:console.error("Keys Missing:",e)},j=function(e){H(e,["left","variableWidth","slideCount","slidesToShow","slideWidth"]),e.vertical?n=(e.unslick?e.slideCount:e.slideCount+2*e.slidesToShow)*e.slideHeight:t=N(e)*e.slideWidth;var t,n,r,i,s,a={opacity:1,transition:"",WebkitTransition:""};return e.useTransform?(r=e.vertical?"translate3d(0px, "+e.left+"px, 0px)":"translate3d("+e.left+"px, 0px, 0px)",i=e.vertical?"translate3d(0px, "+e.left+"px, 0px)":"translate3d("+e.left+"px, 0px, 0px)",s=e.vertical?"translateY("+e.left+"px)":"translateX("+e.left+"px)",a=o()(o()({},a),{},{WebkitTransform:r,transform:i,msTransform:s})):e.vertical?a.top=e.left:a.left=e.left,e.fade&&(a={opacity:1}),t&&(a.width=t),n&&(a.height=n),window&&!window.addEventListener&&window.attachEvent&&(e.vertical?a.marginTop=e.left+"px":a.marginLeft=e.left+"px"),a},A=function(e){H(e,["left","variableWidth","slideCount","slidesToShow","slideWidth","speed","cssEase"]);var t=j(e);return e.useTransform?(t.WebkitTransition="-webkit-transform "+e.speed+"ms "+e.cssEase,t.transition="transform "+e.speed+"ms "+e.cssEase):e.vertical?t.transition="top "+e.speed+"ms "+e.cssEase:t.transition="left "+e.speed+"ms "+e.cssEase,t},I=function(e){if(e.unslick)return 0;H(e,["slideIndex","trackRef","infinite","centerMode","slideCount","slidesToShow","slidesToScroll","slideWidth","listWidth","variableWidth","slideHeight"]);var t=e.slideIndex,n=e.trackRef,r=e.infinite,i=e.centerMode,o=e.slideCount,s=e.slidesToShow,a=e.slidesToScroll,l=e.slideWidth,c=e.listWidth,u=e.variableWidth,d=e.slideHeight,p=e.fade,f=e.vertical;if(p||1===e.slideCount)return 0;if(p=0,r?(p=-D(e),o%a!=0&&o<t+a&&(p=-(o<t?s-(t-o):o%a)),i&&(p+=parseInt(s/2))):(o%a!=0&&o<t+a&&(p=s-o%a),i&&(p=parseInt(s/2))),y=f?t*d*-1+p*d:t*l*-1+p*l,!0===u){var h=n&&n.node;if(v=t+D(e),y=(o=h&&h.childNodes[v])?-1*o.offsetLeft:0,!0===i){for(var v=r?t+D(e):t,y=(o=h&&h.children[v],0),g=0;g<v;g++)y-=h&&h.children[g]&&h.children[g].offsetWidth;y=(y-=parseInt(e.centerPadding))+(o&&(c-o.offsetWidth)/2)}}return y},D=function(e){return e.unslick||!e.infinite?0:e.variableWidth?e.slideCount:e.slidesToShow+(e.centerMode?1:0)},N=function(e){return 1===e.slideCount?1:D(e)+e.slideCount+w(e)},q=function(e){return e.targetSlide>e.currentSlide?e.targetSlide>e.currentSlide+function(e){var t=e.slidesToShow,n=e.centerMode,r=e.rtl;return e=e.centerPadding,n?(n=(t-1)/2+1,0<parseInt(e)&&(n+=1),r&&t%2==0&&(n+=1),n):r?0:t-1}(e)?"left":"right":e.targetSlide<e.currentSlide-function(e){var t=e.slidesToShow,n=e.centerMode,r=e.rtl;return e=e.centerPadding,n?(n=(t-1)/2+1,0<parseInt(e)&&(n+=1),r||t%2!=0||(n+=1),n):r?t-1:0}(e)?"right":"left"},X=Object.keys(S);function Y(e){var t,n,r,i=e.rtl?e.slideCount-1-e.index:e.index,o=i<0||i>=e.slideCount;return e.centerMode?(r=Math.floor(e.slidesToShow/2),n=(i-e.currentSlide)%e.slideCount==0,i>e.currentSlide-r-1&&i<=e.currentSlide+r&&(t=!0)):t=e.currentSlide<=i&&i<e.currentSlide+e.slidesToShow,{"slick-slide":!0,"slick-active":t,"slick-center":n,"slick-cloned":o,"slick-current":i===(e.targetSlide<0?e.targetSlide+e.slideCount:e.targetSlide>=e.slideCount?e.targetSlide-e.slideCount:e.targetSlide)}}function F(e,t){return e.key+"-"+t}var B=function(e){function t(){var e;s()(this,t);for(var n,r,i,o=arguments.length,a=new Array(o),d=0;d<o;d++)a[d]=arguments[d];return n=this,r=t,i=[].concat(a),r=u()(r),e=l()(n,c()()?Reflect.construct(r,i||[],u()(n).constructor):r.apply(n,i)),p()(e,"node",null),p()(e,"handleRef",(function(t){e.node=t})),e}return d()(t,e),a()(t,[{key:"render",value:function(){var e=function(e){var t,n=[],r=[],i=[],s=f.a.Children.count(e.children),a=T(e),l=O(e);return f.a.Children.forEach(e.children,(function(c,u){var d,p={message:"children",index:u,slidesToScroll:e.slidesToScroll,currentSlide:e.currentSlide},h=!e.lazyLoad||e.lazyLoad&&0<=e.lazyLoadedList.indexOf(u)?c:f.a.createElement("div",null),v=(y={},void 0!==(v=o()(o()({},e),{},{index:u})).variableWidth&&!1!==v.variableWidth||(y.width=v.slideWidth),v.fade&&(y.position="relative",v.vertical&&v.slideHeight?y.top=-v.index*parseInt(v.slideHeight):y.left=-v.index*parseInt(v.slideWidth),y.opacity=v.currentSlide===v.index?1:0,y.zIndex=v.currentSlide===v.index?999:998,v.useCSS)&&(y.transition="opacity "+v.speed+"ms "+v.cssEase+", visibility "+v.speed+"ms "+v.cssEase),y),y=h.props.className||"",S=Y(o()(o()({},e),{},{index:u}));n.push(f.a.cloneElement(h,{key:"original"+F(h,u),"data-index":u,className:g()(S,y),tabIndex:"-1","aria-hidden":!S["slick-active"],style:o()(o()({outline:"none"},h.props.style||{}),v),onClick:function(t){h.props&&h.props.onClick&&h.props.onClick(t),e.focusOnSelect&&e.focusOnSelect(p)}})),e.infinite&&1<s&&!1===e.fade&&!e.unslick&&((d=s-u)<=D(e)&&(a<=(t=-d)&&(h=c),S=Y(o()(o()({},e),{},{index:t})),r.push(f.a.cloneElement(h,{key:"precloned"+F(h,t),"data-index":t,tabIndex:"-1",className:g()(S,y),"aria-hidden":!S["slick-active"],style:o()(o()({},h.props.style||{}),v),onClick:function(t){h.props&&h.props.onClick&&h.props.onClick(t),e.focusOnSelect&&e.focusOnSelect(p)}}))),(t=s+u)<l&&(h=c),S=Y(o()(o()({},e),{},{index:t})),i.push(f.a.cloneElement(h,{key:"postcloned"+F(h,t),"data-index":t,tabIndex:"-1",className:g()(S,y),"aria-hidden":!S["slick-active"],style:o()(o()({},h.props.style||{}),v),onClick:function(t){h.props&&h.props.onClick&&h.props.onClick(t),e.focusOnSelect&&e.focusOnSelect(p)}})))})),e.rtl?r.concat(n,i).reverse():r.concat(n,i)}(this.props),t={onMouseEnter:(t=this.props).onMouseEnter,onMouseOver:t.onMouseOver,onMouseLeave:t.onMouseLeave};return f.a.createElement("div",i()({ref:this.handleRef,className:"slick-track",style:this.props.trackStyle},t),e)}}])}(f.a.PureComponent),G=function(e){function t(){return s()(this,t),e=this,n=t,r=arguments,n=u()(n),l()(e,c()()?Reflect.construct(n,r||[],u()(e).constructor):n.apply(e,r));var e,n,r}return d()(t,e),a()(t,[{key:"clickHandler",value:function(e,t){t.preventDefault(),this.props.clickHandler(e)}},{key:"render",value:function(){for(var e=(u=this.props).onMouseEnter,t=u.onMouseOver,n=u.onMouseLeave,r=u.infinite,i=u.slidesToScroll,s=u.slidesToShow,a=u.slideCount,l=u.currentSlide,c=function(e){return e=e.infinite?Math.ceil(e.slideCount/e.slidesToScroll):Math.ceil((e.slideCount-e.slidesToShow)/e.slidesToScroll)+1}({slideCount:a,slidesToScroll:i,slidesToShow:s,infinite:r}),u={onMouseEnter:e,onMouseOver:t,onMouseLeave:n},d=[],p=0;p<c;p++){var h=(p+1)*i-1,v=(h=r?h:m(h,0,a-1))-(i-1);v=r?v:m(v,0,a-1),h=g()({"slick-active":r?v<=l&&l<=h:l===v}),v=this.clickHandler.bind(this,{message:"dots",index:p,slidesToScroll:i,currentSlide:l}),d=d.concat(f.a.createElement("li",{key:p,className:h},f.a.cloneElement(this.props.customPaging(p),{onClick:v})))}return f.a.cloneElement(this.props.appendDots(d),o()({className:this.props.dotsClass},u))}}])}(f.a.PureComponent);function U(e,t,n){return t=u()(t),l()(e,c()()?Reflect.construct(t,n||[],u()(e).constructor):t.apply(e,n))}var V=function(e){function t(){return s()(this,t),U(this,t,arguments)}return d()(t,e),a()(t,[{key:"clickHandler",value:function(e,t){t&&t.preventDefault(),this.props.clickHandler(e,t)}},{key:"render",value:function(){var e={"slick-arrow":!0,"slick-prev":!0},t=this.clickHandler.bind(this,{message:"previous"});!this.props.infinite&&(0===this.props.currentSlide||this.props.slideCount<=this.props.slidesToShow)&&(e["slick-disabled"]=!0,t=null),e={key:"0","data-role":"none",className:g()(e),style:{display:"block"},onClick:t},t={currentSlide:this.props.currentSlide,slideCount:this.props.slideCount};return t=this.props.prevArrow?f.a.cloneElement(this.props.prevArrow,o()(o()({},e),t)):f.a.createElement("button",i()({key:"0",type:"button"},e)," ","Previous")}}])}(f.a.PureComponent),K=function(e){function t(){return s()(this,t),U(this,t,arguments)}return d()(t,e),a()(t,[{key:"clickHandler",value:function(e,t){t&&t.preventDefault(),this.props.clickHandler(e,t)}},{key:"render",value:function(){var e={"slick-arrow":!0,"slick-next":!0},t=this.clickHandler.bind(this,{message:"next"});P(this.props)||(e["slick-disabled"]=!0,t=null),e={key:"1","data-role":"none",className:g()(e),style:{display:"block"},onClick:t},t={currentSlide:this.props.currentSlide,slideCount:this.props.slideCount};return t=this.props.nextArrow?f.a.cloneElement(this.props.nextArrow,o()(o()({},e),t)):f.a.createElement("button",i()({key:"1",type:"button"},e)," ","Next")}}])}(f.a.PureComponent),Z=n(15),$=["animating"],J=function(e){function t(e){s()(this,t),r=this,a=t,e=[e],a=u()(a),n=l()(r,c()()?Reflect.construct(a,e||[],u()(r).constructor):a.apply(r,e)),p()(n,"listRefHandler",(function(e){return n.list=e})),p()(n,"trackRefHandler",(function(e){return n.track=e})),p()(n,"adaptHeight",(function(){var e;n.props.adaptiveHeight&&n.list&&(e=n.list.querySelector('[data-index="'.concat(n.state.currentSlide,'"]')),n.list.style.height=C(e)+"px")})),p()(n,"componentDidMount",(function(){n.props.onInit&&n.props.onInit(),n.props.lazyLoad&&0<(e=_(o()(o()({},n.props),n.state))).length&&(n.setState((function(t){return{lazyLoadedList:t.lazyLoadedList.concat(e)}})),n.props.onLazyLoad)&&n.props.onLazyLoad(e);var e,t=o()({listRef:n.list,trackRef:n.track},n.props);n.updateState(t,!0,(function(){n.adaptHeight(),n.props.autoplay&&n.autoPlay("playing")})),"progressive"===n.props.lazyLoad&&(n.lazyLoadTimer=setInterval(n.progressiveLazyLoad,1e3)),n.ro=new Z.a((function(){n.state.animating?(n.onWindowResized(!1),n.callbackTimers.push(setTimeout((function(){return n.onWindowResized()}),n.props.speed))):n.onWindowResized()})),n.ro.observe(n.list),document.querySelectorAll&&Array.prototype.forEach.call(document.querySelectorAll(".slick-slide"),(function(e){e.onfocus=n.props.pauseOnFocus?n.onSlideFocus:null,e.onblur=n.props.pauseOnFocus?n.onSlideBlur:null})),window.addEventListener?window.addEventListener("resize",n.onWindowResized):window.attachEvent("onresize",n.onWindowResized)})),p()(n,"componentWillUnmount",(function(){n.animationEndCallback&&clearTimeout(n.animationEndCallback),n.lazyLoadTimer&&clearInterval(n.lazyLoadTimer),n.callbackTimers.length&&(n.callbackTimers.forEach((function(e){return clearTimeout(e)})),n.callbackTimers=[]),window.addEventListener?window.removeEventListener("resize",n.onWindowResized):window.detachEvent("onresize",n.onWindowResized),n.autoplayTimer&&clearInterval(n.autoplayTimer),n.ro.disconnect()})),p()(n,"componentDidUpdate",(function(e){n.checkImagesLoad(),n.props.onReInit&&n.props.onReInit(),n.props.lazyLoad&&0<(t=_(o()(o()({},n.props),n.state))).length&&(n.setState((function(e){return{lazyLoadedList:e.lazyLoadedList.concat(t)}})),n.props.onLazyLoad)&&n.props.onLazyLoad(t),n.adaptHeight();var t,r=o()(o()({listRef:n.list,trackRef:n.track},n.props),n.state),i=n.didPropsChange(e);i&&n.updateState(r,i,(function(){n.state.currentSlide>=f.a.Children.count(n.props.children)&&n.changeSlide({message:"index",index:f.a.Children.count(n.props.children)-n.props.slidesToShow,currentSlide:n.state.currentSlide}),e.autoplay===n.props.autoplay&&e.autoplaySpeed===n.props.autoplaySpeed||(!e.autoplay&&n.props.autoplay?n.autoPlay("playing"):n.props.autoplay?n.autoPlay("update"):n.pause("paused"))}))})),p()(n,"onWindowResized",(function(e){var t;n.debouncedResize&&n.debouncedResize.cancel(),n.debouncedResize=(50,function(e,t,n){var r,i,o=void 0!==(i=(n=n||{}).noTrailing)&&i,s=void 0!==(i=n.noLeading)&&i,a=void 0===(i=n.debounceMode)?void 0:i,l=!1,c=0;function u(){r&&clearTimeout(r)}function d(){for(var n=arguments.length,i=new Array(n),d=0;d<n;d++)i[d]=arguments[d];var p=this,f=Date.now()-c;function h(){c=Date.now(),t.apply(p,i)}function v(){r=void 0}l||(s||!a||r||h(),u(),void 0===a&&e<f?s?(c=Date.now(),o||(r=setTimeout(a?v:h,e))):h():!0!==o&&(r=setTimeout(a?v:h,void 0===a?e-f:e)))}return d.cancel=function(e){e=void 0!==(e=(e||{}).upcomingOnly)&&e,u(),l=!e},d}(50,(function(){return n.resizeWindow(e)}),{debounceMode:!1!==(void 0!==(t=(t||{}).atBegin)&&t)})),n.debouncedResize()})),p()(n,"resizeWindow",(function(){var e,t=!(0<arguments.length&&void 0!==arguments[0])||arguments[0];Boolean(n.track&&n.track.node)&&(e=o()(o()({listRef:n.list,trackRef:n.track},n.props),n.state),n.updateState(e,t,(function(){n.props.autoplay?n.autoPlay("update"):n.pause("paused")})),n.setState({animating:!1}),clearTimeout(n.animationEndCallback),delete n.animationEndCallback)})),p()(n,"updateState",(function(e,t,r){i=e,s=f.a.Children.count(i.children),a=i.listRef,h=Math.ceil(L(a)),v=i.trackRef&&i.trackRef.node,v=Math.ceil(L(v)),p=i.vertical?h:(p=i.centerMode&&2*parseInt(i.centerPadding),"string"==typeof i.centerPadding&&"%"===i.centerPadding.slice(-1)&&(p*=h/100),Math.ceil((h-p)/i.slidesToShow)),l=(a=a&&C(a.querySelector('[data-index="0"]')))*i.slidesToShow,c=void 0===i.currentSlide?i.initialSlide:i.currentSlide,i.rtl&&void 0===i.currentSlide&&(c=s-1-i.initialSlide),u=i.lazyLoadedList||[],d=_(o()(o()({},i),{},{currentSlide:c,lazyLoadedList:u})),s={slideCount:s,slideWidth:p,listWidth:h,trackWidth:v,currentSlide:c,slideHeight:a,listHeight:l,lazyLoadedList:u=u.concat(d)},null===i.autoplaying&&i.autoplay&&(s.autoplaying="playing");var i,s,a,l,c,u,d,p=s,h=(e=o()(o()(o()({},e),p),{},{slideIndex:p.currentSlide}),I(e)),v=(e=o()(o()({},e),{},{left:h}),j(e));!t&&f.a.Children.count(n.props.children)===f.a.Children.count(e.children)||(p.trackStyle=v),n.setState(p,r)})),p()(n,"ssrInit",(function(){if(n.props.variableWidth){var e=0,t=0,r=[],i=D(o()(o()(o()({},n.props),n.state),{},{slideCount:n.props.children.length})),s=w(o()(o()(o()({},n.props),n.state),{},{slideCount:n.props.children.length}));n.props.children.forEach((function(t){r.push(t.props.style.width),e+=t.props.style.width}));for(var a=0;a<i;a++)t+=r[r.length-1-a],e+=r[r.length-1-a];for(var l=0;l<s;l++)e+=r[l];for(var c=0;c<n.state.currentSlide;c++)t+=r[c];var u={width:e+"px",left:-t+"px"};return n.props.centerMode&&(d="".concat(r[n.state.currentSlide],"px"),u.left="calc(".concat(u.left," + (100% - ").concat(d,") / 2 ) ")),{trackStyle:u}}var d=f.a.Children.count(n.props.children),p=(u=o()(o()(o()({},n.props),n.state),{},{slideCount:d}),d=D(u)+w(u)+d,100/n.props.slidesToShow*d);u=-(d=100/d)*(D(u)+n.state.currentSlide)*p/100;return n.props.centerMode&&(u+=(100-d*p/100)/2),{slideWidth:d+"%",trackStyle:{width:p+"%",left:u+"%"}}})),p()(n,"checkImagesLoad",(function(){var e=n.list&&n.list.querySelectorAll&&n.list.querySelectorAll(".slick-slide img")||[],t=e.length,r=0;Array.prototype.forEach.call(e,(function(e){function i(){return++r&&t<=r&&n.onWindowResized()}var o;e.onclick?(o=e.onclick,e.onclick=function(t){o(t),e.parentNode.focus()}):e.onclick=function(){return e.parentNode.focus()},e.onload||(n.props.lazyLoad?e.onload=function(){n.adaptHeight(),n.callbackTimers.push(setTimeout(n.onWindowResized,n.props.speed))}:(e.onload=i,e.onerror=function(){i(),n.props.onLazyLoadError&&n.props.onLazyLoadError()}))}))})),p()(n,"progressiveLazyLoad",(function(){for(var e=[],t=o()(o()({},n.props),n.state),r=n.state.currentSlide;r<n.state.slideCount+w(t);r++)if(n.state.lazyLoadedList.indexOf(r)<0){e.push(r);break}for(var i=n.state.currentSlide-1;i>=-D(t);i--)if(n.state.lazyLoadedList.indexOf(i)<0){e.push(i);break}0<e.length?(n.setState((function(t){return{lazyLoadedList:t.lazyLoadedList.concat(e)}})),n.props.onLazyLoad&&n.props.onLazyLoad(e)):n.lazyLoadTimer&&(clearInterval(n.lazyLoadTimer),delete n.lazyLoadTimer)})),p()(n,"slideHandler",(function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],r=(c=n.props).asNavFor,i=c.beforeChange,s=c.onLazyLoad,a=c.speed,l=c.afterChange,c=n.state.currentSlide,u=(t=function(e){var t=e.waitForAnimate,n=e.animating,r=e.fade,i=e.infinite,s=e.index,a=e.slideCount,l=e.lazyLoad,c=e.currentSlide,u=e.centerMode,d=e.slidesToScroll,p=e.slidesToShow,f=e.useCSS,h=e.lazyLoadedList;if(t&&n)return{};t=s,n={};var v={},y=i?s:m(s,0,a-1);if(r){if(!i&&(s<0||a<=s))return{};s<0?t=s+a:a<=s&&(t=s-a),v={animating:!(n={animating:!0,currentSlide:t,lazyLoadedList:h=l&&h.indexOf(t)<0?h.concat(t):h,targetSlide:t}),targetSlide:t}}else(r=t)<0?(r=t+a,i?a%d!=0&&(r=a-a%d):r=0):!P(e)&&c<t?t=r=c:u&&a<=t?(t=i?a:a-1,r=i?0:a-1):a<=t&&(r=t-a,i?a%d!=0&&(r=0):r=a-p),!i&&a<=t+p&&(r=a-p),s=I(o()(o()({},e),{},{slideIndex:t})),c=I(o()(o()({},e),{},{slideIndex:r})),i||(s===c&&(t=r),s=c),l&&(h=h.concat(_(o()(o()({},e),{},{currentSlide:t})))),f?v={animating:!(n={animating:!0,currentSlide:r,trackStyle:A(o()(o()({},e),{},{left:s})),lazyLoadedList:h,targetSlide:y}),currentSlide:r,trackStyle:j(o()(o()({},e),{},{left:c})),swipeLeft:null,targetSlide:y}:n={currentSlide:r,trackStyle:j(o()(o()({},e),{},{left:c})),lazyLoadedList:h,targetSlide:y};return{state:n,nextState:v}}(o()(o()(o()({index:e},n.props),n.state),{},{trackRef:n.track,useCSS:n.props.useCSS&&!t}))).state,d=t.nextState;u&&(i&&i(c,u.currentSlide),t=u.lazyLoadedList.filter((function(e){return n.state.lazyLoadedList.indexOf(e)<0})),s&&0<t.length&&s(t),!n.props.waitForAnimate&&n.animationEndCallback&&(clearTimeout(n.animationEndCallback),l&&l(c),delete n.animationEndCallback),n.setState(u,(function(){r&&n.asNavForIndex!==e&&(n.asNavForIndex=e,r.innerSlider.slideHandler(e)),d&&(n.animationEndCallback=setTimeout((function(){var e=d.animating,t=v()(d,$);n.setState(t,(function(){n.callbackTimers.push(setTimeout((function(){return n.setState({animating:e})}),10)),l&&l(u.currentSlide),delete n.animationEndCallback}))}),a))})))})),p()(n,"changeSlide",(function(e){var t,r,i,s,a,l,c,u,d,p=1<arguments.length&&void 0!==arguments[1]&&arguments[1],f=o()(o()({},n.props),n.state),h=(e=e,r=(f=f).slidesToScroll,i=f.slidesToShow,s=f.slideCount,a=f.currentSlide,l=f.targetSlide,d=f.lazyLoad,c=f.infinite,u=s%r!=0?0:(s-a)%r,"previous"===e.message?(t=a-(h=0==u?r:i-u),d&&!c&&(t=-1==(i=a-h)?s-1:i),c||(t=l-r)):"next"===e.message?(t=a+(h=0==u?r:u),d&&!c&&(t=(a+r)%s+u),c||(t=l+r)):"dots"===e.message?t=e.index*e.slidesToScroll:"children"===e.message?(t=e.index,c&&(i=q(o()(o()({},f),{},{targetSlide:t})),t>e.currentSlide&&"left"===i?t-=s:t<e.currentSlide&&"right"===i&&(t+=s))):"index"===e.message&&(t=Number(e.index)),t);(0===h||h)&&(!0===p?n.slideHandler(h,p):n.slideHandler(h),n.props.autoplay&&n.autoPlay("update"),n.props.focusOnSelect)&&(d=n.list.querySelectorAll(".slick-current"))[0]&&d[0].focus()})),p()(n,"clickHandler",(function(e){!1===n.clickable&&(e.stopPropagation(),e.preventDefault()),n.clickable=!0})),p()(n,"keyHandler",(function(e){e=e,r=n.props.accessibility,t=n.props.rtl;var t,r=e.target.tagName.match("TEXTAREA|INPUT|SELECT")||!r?"":37===e.keyCode?t?"next":"previous":39===e.keyCode?t?"previous":"next":"";r&&n.changeSlide({message:r})})),p()(n,"selectHandler",(function(e){n.changeSlide(e)})),p()(n,"disableBodyScroll",(function(){window.ontouchmove=function(e){(e=e||window.event).preventDefault&&e.preventDefault(),e.returnValue=!1}})),p()(n,"enableBodyScroll",(function(){window.ontouchmove=null})),p()(n,"swipeStart",(function(e){n.props.verticalSwiping&&n.disableBodyScroll(),e=e,r=n.props.swipe,t=n.props.draggable,"IMG"===e.target.tagName&&x(e);var t,r=!r||!t&&-1!==e.type.indexOf("mouse")?"":{dragging:!0,touchObject:{startX:e.touches?e.touches[0].pageX:e.clientX,startY:e.touches?e.touches[0].pageY:e.clientY,curX:e.touches?e.touches[0].pageX:e.clientX,curY:e.touches?e.touches[0].pageY:e.clientY}};""!==r&&n.setState(r)})),p()(n,"swipeMove",(function(e){(e=function(e,t){var n=t.scrolling,r=t.animating,i=t.vertical,s=t.swipeToSlide,a=t.verticalSwiping,l=t.rtl,c=t.currentSlide,u=t.edgeFriction,d=t.edgeDragged,p=t.onEdge,f=t.swiped,h=t.swiping,v=t.slideCount,y=t.slidesToScroll,g=t.infinite,S=t.touchObject,m=t.swipeEvent,b=t.listHeight,w=t.listWidth;if(!n)return r?x(e):(i&&s&&a&&x(e),n={},r=I(t),S.curX=e.touches?e.touches[0].pageX:e.clientX,S.curY=e.touches?e.touches[0].pageY:e.clientY,S.swipeLength=Math.round(Math.sqrt(Math.pow(S.curX-S.startX,2))),s=Math.round(Math.sqrt(Math.pow(S.curY-S.startY,2))),!a&&!h&&10<s?{scrolling:!0}:(a&&(S.swipeLength=s),h=(l?-1:1)*(S.curX>S.startX?1:-1),a&&(h=S.curY>S.startY?1:-1),s=Math.ceil(v/y),v=z(t.touchObject,a),y=S.swipeLength,g||(0===c&&("right"===v||"down"===v)||s<=c+1&&("left"===v||"up"===v)||!P(t)&&("left"===v||"up"===v))&&(y=S.swipeLength*u,!1===d)&&p&&(p(v),n.edgeDragged=!0),!f&&m&&(m(v),n.swiped=!0),g=a?r+y*h:i?r+y*(b/w)*h:l?r-y*h:r+y*h,n=o()(o()({},n),{},{touchObject:S,swipeLeft:g,trackStyle:j(o()(o()({},t),{},{left:g}))}),Math.abs(S.curX-S.startX)<.8*Math.abs(S.curY-S.startY)||10<S.swipeLength&&(n.swiping=!0,x(e)),n))}(e,o()(o()(o()({},n.props),n.state),{},{trackRef:n.track,listRef:n.list,slideIndex:n.state.currentSlide})))&&(e.swiping&&(n.clickable=!1),n.setState(e))})),p()(n,"swipeEnd",(function(e){var t;(e=function(e,t){var n=t.dragging,r=t.swipe,i=t.touchObject,s=t.listWidth,a=t.touchThreshold,l=t.verticalSwiping,c=t.listHeight,u=t.swipeToSlide,d=t.scrolling,p=t.onSwipe,f=t.targetSlide,h=t.currentSlide,v=t.infinite;if(!n)return r&&x(e),{};n=l?c/a:s/a,r=z(i,l);var y={dragging:!1,edgeDragged:!1,scrolling:!1,swiping:!1,swiped:!1,swipeLeft:null,touchObject:{}};if(!d&&i.swipeLength)if(i.swipeLength>n){x(e),p&&p(r);var g,S,m=v?h:f;switch(r){case"left":case"up":S=m+W(t),g=u?R(t,S):S,y.currentDirection=0;break;case"right":case"down":S=m-W(t),g=u?R(t,S):S,y.currentDirection=1;break;default:g=m}y.triggerSlideHandler=g}else c=I(t),y.trackStyle=A(o()(o()({},t),{},{left:c}));return y}(e,o()(o()(o()({},n.props),n.state),{},{trackRef:n.track,listRef:n.list,slideIndex:n.state.currentSlide})))&&(t=e.triggerSlideHandler,delete e.triggerSlideHandler,n.setState(e),void 0!==t)&&(n.slideHandler(t),n.props.verticalSwiping)&&n.enableBodyScroll()})),p()(n,"touchEnd",(function(e){n.swipeEnd(e),n.clickable=!0})),p()(n,"slickPrev",(function(){n.callbackTimers.push(setTimeout((function(){return n.changeSlide({message:"previous"})}),0))})),p()(n,"slickNext",(function(){n.callbackTimers.push(setTimeout((function(){return n.changeSlide({message:"next"})}),0))})),p()(n,"slickGoTo",(function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(e=Number(e),isNaN(e))return"";n.callbackTimers.push(setTimeout((function(){return n.changeSlide({message:"index",index:e,currentSlide:n.state.currentSlide},t)}),0))})),p()(n,"play",(function(){var e;if(n.props.rtl)e=n.state.currentSlide-n.props.slidesToScroll;else{if(!P(o()(o()({},n.props),n.state)))return!1;e=n.state.currentSlide+n.props.slidesToScroll}n.slideHandler(e)})),p()(n,"autoPlay",(function(e){n.autoplayTimer&&clearInterval(n.autoplayTimer);var t=n.state.autoplaying;if("update"===e){if("hovered"===t||"focused"===t||"paused"===t)return}else if("leave"===e){if("paused"===t||"focused"===t)return}else if("blur"===e&&("paused"===t||"hovered"===t))return;n.autoplayTimer=setInterval(n.play,n.props.autoplaySpeed+50),n.setState({autoplaying:"playing"})})),p()(n,"pause",(function(e){n.autoplayTimer&&(clearInterval(n.autoplayTimer),n.autoplayTimer=null);var t=n.state.autoplaying;"paused"===e?n.setState({autoplaying:"paused"}):"focused"===e?"hovered"!==t&&"playing"!==t||n.setState({autoplaying:"focused"}):"playing"===t&&n.setState({autoplaying:"hovered"})})),p()(n,"onDotsOver",(function(){return n.props.autoplay&&n.pause("hovered")})),p()(n,"onDotsLeave",(function(){return n.props.autoplay&&"hovered"===n.state.autoplaying&&n.autoPlay("leave")})),p()(n,"onTrackOver",(function(){return n.props.autoplay&&n.pause("hovered")})),p()(n,"onTrackLeave",(function(){return n.props.autoplay&&"hovered"===n.state.autoplaying&&n.autoPlay("leave")})),p()(n,"onSlideFocus",(function(){return n.props.autoplay&&n.pause("focused")})),p()(n,"onSlideBlur",(function(){return n.props.autoplay&&"focused"===n.state.autoplaying&&n.autoPlay("blur")})),p()(n,"render",(function(){var e,t,r,s=g()("slick-slider",n.props.className,{"slick-vertical":n.props.vertical,"slick-initialized":!0}),a=b(c=o()(o()({},n.props),n.state),["fade","cssEase","speed","infinite","centerMode","focusOnSelect","currentSlide","lazyLoad","lazyLoadedList","rtl","slideWidth","slideHeight","listHeight","vertical","slidesToShow","slidesToScroll","slideCount","trackStyle","variableWidth","unslick","centerPadding","targetSlide","useCSS"]),l=n.props.pauseOnHover,c=(a=o()(o()({},a),{},{onMouseEnter:l?n.onTrackOver:null,onMouseLeave:l?n.onTrackLeave:null,onMouseOver:l?n.onTrackOver:null,focusOnSelect:n.props.focusOnSelect&&n.clickable?n.selectHandler:null}),(l=(!0===n.props.dots&&n.state.slideCount>=n.props.slidesToShow&&(l=b(c,["dotsClass","slideCount","slidesToShow","currentSlide","slidesToScroll","clickHandler","children","customPaging","infinite","appendDots"]),e=n.props.pauseOnDotsHover,l=o()(o()({},l),{},{clickHandler:n.changeSlide,onMouseEnter:e?n.onDotsLeave:null,onMouseOver:e?n.onDotsOver:null,onMouseLeave:e?n.onDotsLeave:null}),e=f.a.createElement(G,l)),b(c,["infinite","centerMode","currentSlide","slideCount","slidesToShow","prevArrow","nextArrow"]))).clickHandler=n.changeSlide,n.props.arrows&&(t=f.a.createElement(V,l),r=f.a.createElement(K,l)),null);n.props.vertical&&(c={height:n.state.listHeight}),l=null,!1===n.props.vertical?!0===n.props.centerMode&&(l={padding:"0px "+n.props.centerPadding}):!0===n.props.centerMode&&(l={padding:n.props.centerPadding+" 0px"}),c=o()(o()({},c),l),l=n.props.touchMove,c={className:"slick-list",style:c,onClick:n.clickHandler,onMouseDown:l?n.swipeStart:null,onMouseMove:n.state.dragging&&l?n.swipeMove:null,onMouseUp:l?n.swipeEnd:null,onMouseLeave:n.state.dragging&&l?n.swipeEnd:null,onTouchStart:l?n.swipeStart:null,onTouchMove:n.state.dragging&&l?n.swipeMove:null,onTouchEnd:l?n.touchEnd:null,onTouchCancel:n.state.dragging&&l?n.swipeEnd:null,onKeyDown:n.props.accessibility?n.keyHandler:null},l={className:s,dir:"ltr",style:n.props.style};return n.props.unslick&&(c={className:"slick-list"},l={className:s,style:n.props.style}),f.a.createElement("div",l,n.props.unslick?"":t,f.a.createElement("div",i()({ref:n.listRefHandler},c),f.a.createElement(B,i()({ref:n.trackRefHandler},a),n.props.children)),n.props.unslick?"":r,n.props.unslick?"":e)})),n.list=null,n.track=null,n.state=o()(o()({},y),{},{currentSlide:n.props.initialSlide,targetSlide:n.props.initialSlide||0,slideCount:f.a.Children.count(n.props.children)}),n.callbackTimers=[],n.clickable=!0,n.debouncedResize=null;var n,r,a=n.ssrInit();return n.state=o()(o()({},n.state),a),n}return d()(t,e),a()(t,[{key:"didPropsChange",value:function(e){for(var t=!1,n=0,r=Object.keys(this.props);n<r.length;n++){var i=r[n];if(!e.hasOwnProperty(i)){t=!0;break}if("object"!==h()(e[i])&&"function"!=typeof e[i]&&!isNaN(e[i])&&e[i]!==this.props[i]){t=!0;break}}return t||f.a.Children.count(this.props.children)!==f.a.Children.count(e.children)}}])}(f.a.Component),Q=(r=n(12),n.n(r));n=function(e){function t(e){var n,r,i;return s()(this,t),r=this,i=t,e=[e],i=u()(i),n=l()(r,c()()?Reflect.construct(i,e||[],u()(r).constructor):i.apply(r,e)),p()(n,"innerSliderRefHandler",(function(e){return n.innerSlider=e})),p()(n,"slickPrev",(function(){return n.innerSlider.slickPrev()})),p()(n,"slickNext",(function(){return n.innerSlider.slickNext()})),p()(n,"slickGoTo",(function(e){return n.innerSlider.slickGoTo(e,1<arguments.length&&void 0!==arguments[1]&&arguments[1])})),p()(n,"slickPause",(function(){return n.innerSlider.pause("paused")})),p()(n,"slickPlay",(function(){return n.innerSlider.autoPlay("play")})),n.state={breakpoint:null},n._responsiveMediaHandlers=[],n}return d()(t,e),a()(t,[{key:"media",value:function(e,t){function n(e){e.matches&&t()}var r=window.matchMedia(e);r.addListener(n),n(r),this._responsiveMediaHandlers.push({mql:r,query:e,listener:n})}},{key:"componentDidMount",value:function(){var e,t,n=this;this.props.responsive&&((e=this.props.responsive.map((function(e){return e.breakpoint}))).sort((function(e,t){return e-t})),e.forEach((function(t,r){r=0===r?Q()({minWidth:0,maxWidth:t}):Q()({minWidth:e[r-1]+1,maxWidth:t}),k()&&n.media(r,(function(){n.setState({breakpoint:t})}))})),t=Q()({minWidth:e.slice(-1)[0]}),k())&&this.media(t,(function(){n.setState({breakpoint:null})}))}},{key:"componentWillUnmount",value:function(){this._responsiveMediaHandlers.forEach((function(e){e.mql.removeListener(e.listener)}))}},{key:"render",value:function(){var e=this,t=this.state.breakpoint?"unslick"===(r=this.props.responsive.filter((function(t){return t.breakpoint===e.state.breakpoint})))[0].settings?"unslick":o()(o()(o()({},S),this.props),r[0].settings):o()(o()({},S),this.props);t.centerMode&&(t.slidesToScroll,t.slidesToScroll=1),t.fade&&(t.slidesToShow,t.slidesToScroll,t.slidesToShow=1,t.slidesToScroll=1);var n=(n=f.a.Children.toArray(this.props.children)).filter((function(e){return"string"==typeof e?!!e.trim():!!e}));t.variableWidth&&(1<t.rows||1<t.slidesPerRow)&&(console.warn("variableWidth is not supported in case of rows > 1 or slidesPerRow > 1"),t.variableWidth=!1);for(var r,s,a=[],l=null,c=0;c<n.length;c+=t.rows*t.slidesPerRow){for(var u=[],d=c;d<c+t.rows*t.slidesPerRow;d+=t.slidesPerRow){for(var p=[],h=d;h<d+t.slidesPerRow&&(t.variableWidth&&n[h].props.style&&(l=n[h].props.style.width),!(h>=n.length));h+=1)p.push(f.a.cloneElement(n[h],{key:100*c+10*d+h,tabIndex:-1,style:{width:"".concat(100/t.slidesPerRow,"%"),display:"inline-block"}}));u.push(f.a.createElement("div",{key:10*c+d},p))}t.variableWidth?a.push(f.a.createElement("div",{key:c,style:{width:l}},u)):a.push(f.a.createElement("div",{key:c},u))}return"unslick"===t?(r="regular slider "+(this.props.className||""),f.a.createElement("div",{className:r},n)):(a.length<=t.slidesToShow&&!t.infinite&&(t.unslick=!0),f.a.createElement(J,i()({style:this.props.style,ref:this.innerSliderRefHandler},(s=t,X.reduce((function(e,t){return s.hasOwnProperty(t)&&(e[t]=s[t]),e}),{}))),a))}}])}(f.a.Component),t.default=n}],r={},t.m=n,t.c=r,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var i in e)t.d(r,i,function(t){return e[t]}.bind(null,i));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=22);function t(e){var i;return(r[e]||(i=r[e]={i:e,l:!1,exports:{}},n[e].call(i.exports,i,i.exports,t),i.l=!0,i)).exports}var n,r}));
//# sourceMappingURL=react-slick.min.js.map