#!/usr/bin/env python3
"""
简化的Flask应用启动脚本
"""

from app import create_app

if __name__ == '__main__':
    try:
        app = create_app()
        print("Flask应用创建成功！")
        print("正在启动服务器...")
        print("访问地址: http://localhost:5001")
        app.run(host='localhost', port=5001, debug=False, use_reloader=False)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
