## 🧩 系统名称：智能任务协调系统

### 一、系统目标
构建一个以《00 CAF项目进展情况.xlsx》为数据源，具备**自动任务分配**、**工作量可视化**、**多角色协作**的项目任务管理系统，实现项目任务的智能分配与动态协调。
**核心价值**: 通过数据驱动的可视化界面，提升多项目、多优先级背景下的任务分配效率与资源协调透明度
- **系统定位**: 内网多人协作的任务分配与协调系统
**系统核心目标**
该系统需实现以下主要功能目标：
- **目标1：** 能够快速、清晰地统计和展示每位人员当前的工作负荷情况（工作量可视化）。
- **目标2：** 当有新任务创建时，系统能根据自动分配原则，`自动推荐`或`直接分配`给工作量最轻或最合适的人员（需经部长确认）。
- **目标3：** `主任`拥有最高协调权，可以在自动分配的基础上，对所有任务的分配情况进行查看和手动调整，以确保整体协调与平衡。

---

### 二、数据源与字段映射

#### 2.1 数据来源
- 主数据源：`00 CAF项目进展情况.xlsx` → `CAF项目状态` sheet
- 系统自动解析Excel中的所有列，包括：
  - 项目名称、项目编号、CAF编码、TMT编码
  - 技术负责人、技术难度、阶段、立项时间
  - 各种状态（CBC/TTP/RTP/TTR/图纸等）
  - 进度说明、问题记录、会议纪要等

#### 2.2 核心显示字段（主界面）
系统上方有“新建任务”、“项目自动分配：（新项目自动分配到设计师）”、“导入excel”、“导出excel”、“刷新”、“选择状态”（筛选功能）
系统主界面表格仅显示以下**8个核心字段**，用于快速决策：
1. 序号
2. 项目名称
3. 项目编号
4. TMT编码
5. 技术负责人
6. 技术难度
7. 进度
8. 阶段
9. 立项时间
10.完成时间
11.操作（一个眼睛图标）鼠标放上面显示“查看详情”
##### 分层显示设计
- **主界面**：只显示8个核心字段，保证界面清晰、决策高效
- **详情视图**：点击展开后显示所有原始字段，确保数据完整性
- **导出功能**：支持导出完整数据，包含所有字段信息
> 其余字段可通过点击操作展开详情模态框查看。

---

### 三、用户角色与权限

##### 超级管理者 (Super Admin)
- **核心权限**：拥有系统的全部权限
- **账户管理**：增删改查所有管理员账户（即可管理主任、部长等角色的账户）
- **系统配置**：系统参数设置、权限模板配置、数据备份管理
- **全局监控**：查看所有用户操作日志、系统性能监控

##### 主任 (Director)
- **核心权限**：
  - 查看系统内所有项目信息
  - 具备任务分配功能
  - 可修改系统自动分配任务的规则或结果
- **协调权限**：拥有最高协调权，可以在自动分配的基础上，对所有任务的分配情况进行查看和手动调整
- **全局视图**：查看所有部门、所有项目的整体协调与平衡情况

##### 部长 (Manager)
- **核心权限**：
  - 创建、编辑、修改项目任务
  - 评定或修改项目的技术难度等级
  - 拥有手动分配任务的权限
  - 对必须完成的项目进行最终确认，并将其纳入分配队列
- **确认环节**：自动分配流程必须设置一个`部长确认`环节，只有在部长确认后，任务才会正式按规则分配下去

##### 设计师/普通人员 (Designer/Staff)
- **核心权限**：
  - 新增项目表格或任务（例如，创建新项目条目）
  - 修改其本人负责的表格内容或任务信息
  - 仅可查看与自身工作相关的项目资料和信息
- **工作范围**：限于个人负责的任务和项目

#### 3.1.2 权限控制矩阵
```
权限矩阵表：
┌─────────────────┬─────────┬─────────┬─────────┬─────────────┐
│ 功能模块         │ 超级管理 │ 主任    │ 部长    │ 设计师/普通  │
├─────────────────┼─────────┼─────────┼─────────┼─────────────┤
│ 用户管理        │ ✓       │ ✗       │ ✗       │ ✗          │
│ 项目管理        │ ✓       │ ✓       │ ✓       │ 仅查看      │
│ 任务分配        │ ✓       │ ✓       │ ✓       │ 仅查看      │
│ 自动分配规则    │ ✓       │ ✓       │ ✗       │ ✗          │
│ 部长确认        │ ✓       │ ✓       │ ✓       │ ✗          │
│ 全局协调        │ ✓       │ ✓       │ ✗       │ ✗          │
│ 数据导出        │ ✓       │ ✓       │ ✓       │ 部分        │
└─────────────────┴─────────┴─────────┴─────────┴─────────────┘
---

### 四、任务自动分配机制

#### 4.1 分配流程
```
任务创建 → 部长评定 → 部长确认 → 自动分配 → 主任协调 → 任务执行
    ↓           ↓         ↓         ↓         ↓         ↓
  设计师     技术难度   纳入队列   算法分配   手动调整   开始执行
  提交任务   等级评定   分配确认   人员匹配   整体平衡   任务执行
```

#### 4.2 自动分配算法（综合评分模型）
系统根据以下维度为每位设计师计算“适配分数”，选择分数最高且负荷最轻者：

| 维度         | 权重  | 说明                                 |
|--------------|-------|--------------------------------------|
| 时间紧急程度 | 25%   | 任务时限紧急程度、根据立项时间、FAI日期、任务时间要求等               |
| 技术难度     | 20%   | 由部长评定                                       |
| 当前项目数量 | 15%   | 该人员当前负责的项目总数、并行任务数量、项目重叠度   |
| 项目等级     | 20%   | 由部长评定（P1-P5）                  |
| 人员技能匹配 | 15%   | 根据技术领域、历史项目匹配度、负责产品类型等      |
| 项目完成度   | 5%    | 该人员当前项目的进度、历史完成率、工作质量评分            |

##### 综合评分算法
```
综合评分 = (时间评分 × 0.25) + (技术难度评分 × 0.20) + 
          (项目数量评分 × 0.15) + (项目等级评分 × 0.20) + 
          (人员匹配评分 × 0.15) + (项目完成度评分 × 0.05)

人员选择策略：选择综合评分最高且当前工作量最轻的人员
```

#### 4.3 部长确认环节
- 自动分配结果必须经**部长确认**后方可正式分配；
- 
- 部长可手动调整分配结果；
- 确认后任务自动下发至对应设计师。

---

### 五、核心功能模块

#### 5.1 仪表盘（Dashboard）
- 总项目数、进行中任务数、超时任务数、本周待完成数；
- 人员负荷分布图（堆叠柱状图，按任务等级着色）；
- 项目进度对比图（圆环图/雷达图）；
- 项目阶段统计图（柱状图/圆环图）；
- 项目优先级分存。

#### 5.2 项目中心（Project Hub）
- 支持项目的增删改查；
- 项目等级：P1（紧急重要）~ P5（待定）；
- 项目状态：规划中、进行中、已完成、已暂停、已取消。


#### 5.3 任务工作台（Task Workspace）
- 表格视图：显示核心8字段，支持筛选、排序、行内编辑；
- 看板视图：按状态拖拽任务；
- 详情视图：点击行展开完整Excel字段（模态框）；
- 支持Excel导出（含所有字段）。
#### **分层显示优势**
- **操作效率**：主界面简洁，快速进行任务分配决策
- **信息完整**：详情视图提供100%完整信息，满足深度分析需求
- **用户体验**：平衡了操作效率和信息完整性，是管理系统的标准最佳实践
- **可定制性**：支持高级用户自定义显示列，包括非关键项点

#### 视图模式
提供三种模式切换，满足不同协作习惯：
- **表格视图** (核心) - 支持智能分层显示
- **看板视图** (Kanban，基于状态拖拽)
- **日历视图**

#### 表格视图功能
- **列定义**:
  - 任务名称
  - 所属项目（下拉选择）
  - 任务等级（五星评分组件或下拉选择P1-P5）
  - 负责人（人员下拉选择）
  - 预计工时
  - 截止日期
  - 状态（进行中/待审核/已完成等）
  - 操作（编辑、删除、保存、展开详情）

- **核心功能**:
  - 支持列筛选、排序
  - 行内编辑、批量操作
  - 新建任务：表格顶部"新建"按钮，添加可编辑空行
  - 智能分层显示：核心字段主显示 + 完整字段详情展开
  - 详情查看：点击"查看详情"按钮，弹出模态框显示所有字段信息
  - 快速操作：支持批量选择、批量修改、批量分配等操作

#### 高级筛选器
固定在顶部，可组合筛选：
- 人员：[张三]
- 等级：[P0, P1]
- 项目：[项目A]
- 快速定位高风险关键任务

#### 5.4 人员视图（Team View）
- 每位设计师的负荷可视化（饼图+水波图）；
- 任务等级分布（极坐标图）；
- 负责的任务列表，支持重新分配；
- 设计师的工作完成情况。

#### 5.5 数据分析（Insights）
- 散点图：预计工时 vs 实际耗时；
- 热力图：一周内任务密集度；
- 支持下钻分析。
### 5.5.1 数据分析模块 (Insights) - "数据叙事"

#### 设计目标
将系统数据转化为洞察，驱动决策。

#### 预设图表
- **散点图 (Scatter Chart)**:
  - X轴：任务预计工时
  - Y轴：实际耗时
  - 点的大小：任务等级
  - 用途：分析估算准确性

- **柱状图对比 (Bar Chart)**:
  - 不同项目间任务完成数量的对比

- **热力图 (Heatmap)**:
  - 展示一周内每天、不同人员的任务密集度

#### 交互特性
- 所有图表支持**下钻 (Drill-down)** 和**联动 (Brushing & Linking)**
- 点击散点图中异常点，下方表格自动筛选出该任务详细信息

---

#### 智能分层显示策略

##### 1. **主界面 - 核心判断项点**
- **显示原则**：只显示用于判断工作量的关键字段，保证界面清晰、决策高效
- **核心字段**：
  - 任务名称
  - 负责人（人员下拉选择）
  - 优先级/等级（P1-P5等级标识）
  - 预计工时
  - 截止日期
  - 状态（进行中/待审核/已完成等）
  - 所属项目（下拉选择）
- **界面特点**：简洁清晰，聚焦核心决策信息，便于快速评估人员工作负荷
- **设计目标**：类似于简化后的Excel表格，只包含最重要的几列，提升任务分配操作效率

##### 2. **详情展开 - 完整信息查看**
- **展开方式**：点击任意任务行，弹出模态框或右侧展开面板，完整显示该任务的所有字段
- **显示内容**：Excel表中的所有原始字段和内容，100%完整信息展示
- **交互特性**：
  - 支持折叠/展开，不占用主界面空间
  - 满足偶尔查看完整信息的需求
  - 支持行内编辑完整字段
  - 实时同步更新到数据存储
  - 查看完毕后关闭详情页，视线回到简洁的主列表

### 六、数据存储与导入

#### 6.1 存储方式
- 使用JSON文件存储所有数据（无需数据库）；
- 文件结构：
  - `tasks.json`：所有任务数据（含Excel所有字段）；
  - `projects.json`：项目信息；
  - `users.json`：用户信息；
  - `config.json`：系统配置。
  - 基于文件系统的数据持久化方案，无需外部数据库，系统平台直接管理数据存储。采用"后台全量存储，前台分层显示"的核心策略。
#### 6.2 Excel导入功能
- 支持拖拽上传Excel；
- 自动识别表头，映射字段；
- 支持字段标记（哪些是核心字段、哪些是辅助字段）；
- 导入后数据完整存储，支持后续分析。

---

### 七、界面设计与交互

- **设计风格**：浅色基调，Ant Design 组件库；
- **主色**：科技蓝 (#1890FF)；
- **响应式**：支持桌面、平板、移动端；
- **动效**：平滑过渡、行操作动画、图表更新动画。
界面设计规范

### 7.1 整体布局
- **顶部导航栏**: Logo、主菜单、用户信息、通知中心
- **左侧边栏**: 功能模块导航、快捷操作
- **主内容区**: 数据展示、操作界面
- **底部状态栏**: 系统状态、在线用户数、数据更新时间

### 7.2 设计风格
- **设计风格**: 现代简约 (Modern Minimalism)
- **基调**: 浅色基调，采用Ant Design设计语言和组件
- **一致性**: 保证内部一致性和开发效率

### 7.3 色彩体系
- **主色**: 科技蓝（#1890FF，代表效率、信任）
- **等级色**: 5个任务等级定义从红色到绿色的渐变色
  - P0-紧急：红色
  - P1-高：橙色
  - P2-中：黄色
  - P3-低：浅绿
  - P4-无关紧要：无色
- **状态色**: 完成-绿色、进行中-蓝色、暂停-灰色、超时-红色
- **辅助色**: #52C41A (成功绿)、#FAAD14 (警告橙)、#F5222D (错误红)
- **中性色**: #F5F5F5 (背景灰)、#D9D9D9 (边框灰)、#262626 (文字黑)

###7.4 响应式设计
- **桌面端**: 1200px+ (完整功能)
- **平板端**: 768px-1199px (核心功能)
- **移动端**: <768px (基础功能)

### 7.5 动效设计
- **模块切换**: 模块之间的切换过渡动画
- **表格操作**: 行新增、删除的平滑动画
- **图表更新**: 数据更新时的过渡动画
- **交互反馈**: 按钮点击、卡片悬停的微交互反馈
---

### 八、安全与权限

- 基于JWT的身份认证；
- RBAC权限控制（角色隔离）；
- 操作日志记录；
- 数据定期备份与恢复机制。

---

### 九、扩展性设计

- 支持插件机制（如对接OA、ERP等）；
- 支持多租户（未来可扩展为多部门使用）；
- API接口开放，支持第三方集成。

---
**版本记录**：
- v1.0 | 2025年9月 | 初始版本 创建者：宁明龙