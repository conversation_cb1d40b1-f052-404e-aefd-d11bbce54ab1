/**
 * 通用类型定义
 */

// 用户角色枚举
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  DIRECTOR = 'director',
  MANAGER = 'manager',
  DESIGNER = 'designer',
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

// 项目优先级枚举
export enum ProjectPriority {
  P0 = 'P0',
  P1 = 'P1',
  P2 = 'P2',
  P3 = 'P3',
  P4 = 'P4',
  P5 = 'P5',
}

// 项目状态枚举
export enum ProjectStatus {
  PLANNING = 'planning',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
}

// 项目阶段枚举
export enum ProjectStage {
  INITIATION = 'initiation',
  DESIGN = 'design',
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  DEPLOYMENT = 'deployment',
  MAINTENANCE = 'maintenance',
}

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  BLOCKED = 'blocked',
}

// 任务优先级枚举
export enum TaskPriority {
  P0 = 'P0',
  P1 = 'P1',
  P2 = 'P2',
  P3 = 'P3',
  P4 = 'P4',
}

// 任务类型枚举
export enum TaskType {
  DESIGN = 'design',
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  REVIEW = 'review',
  DOCUMENTATION = 'documentation',
  MEETING = 'meeting',
  OTHER = 'other',
}

// 用户接口
export interface User {
  id: string;
  username: string;
  email: string;
  full_name: string;
  phone?: string;
  department: string;
  position: string;
  role: UserRole;
  status: UserStatus;
  skills: string[];
  experience_level: number;
  max_concurrent_tasks: number;
  work_efficiency: number;
  total_tasks_completed: number;
  average_task_rating: number;
  last_login_at?: string;
  login_count: number;
  created_at: string;
  updated_at: string;
}

// 项目接口
export interface Project {
  id: string;
  name: string;
  code: string;
  caf_code?: string;
  tmt_code?: string;
  description?: string;
  priority: ProjectPriority;
  status: ProjectStatus;
  stage: ProjectStage;
  difficulty_level: number;
  start_date?: string;
  end_date?: string;
  deadline?: string;
  estimated_hours: number;
  actual_hours: number;
  owner_id?: string;
  technical_lead_id?: string;
  team_members: string[];
  progress_percentage: number;
  progress_notes?: string;
  issues: ProjectIssue[];
  meeting_notes: MeetingNote[];
  tags: string[];
  category?: string;
  department?: string;
  created_at: string;
  updated_at: string;
}

// CAF项目接口
export interface CAFProject {
  id: string;
  sequence_number: number;
  project_name: string;
  project_code?: string;
  caf_code?: string;
  tmt_code?: string;
  chinese_name?: string;
  english_name?: string;
  technical_lead: string;
  technical_manager?: string;
  stage?: string;
  project_start_date?: string;
  fatigue_test_conditions?: string;
  cbc_status?: string;
  drawing_code_version?: string;
  drawing_status?: string;
  ttp_code_version?: string;
  ttp_status?: string;
  rtp_code_version?: string;
  rtp_status?: string;
  ttr_code_version?: string;
  ttr_status?: string;
  internal_drawing_release_time?: string;
  fai_date?: string;
  description?: string;
  caf_onsite_discussion_1002?: string;
  project_progress_1?: string;
  project_progress_2?: string;
  project_progress_3?: string;
  caf_issues_discussion_0319?: string;
  caf_meeting_issues_0414?: string;
  caf_meeting_issues_0616?: string;
  difficulty_level?: string;
  progress_percentage: number;
  completion_date?: string;
  created_at: string;
  updated_at: string;
}

// CAF项目详情接口
export interface CAFProjectDetail {
  core_info: {
    project_name: string;
    project_code?: string;
    tmt_code?: string;
    technical_lead: string;
    stage?: string;
    project_start_date?: string;
  };
  complete_data: {
    [key: string]: any;
  };
  system_info: {
    id: string;
    created_at: string;
    updated_at: string;
  };
}

// 任务接口
export interface Task {
  id: string;
  title: string;
  description?: string;
  project_id: string;
  parent_task_id?: string;
  type: TaskType;
  priority: TaskPriority;
  status: TaskStatus;
  difficulty_level: number;
  start_date?: string;
  due_date?: string;
  estimated_hours: number;
  actual_hours: number;
  completed_at?: string;
  assignee_id?: string;
  creator_id: string;
  reviewer_id?: string;
  required_skills: string[];
  tags: string[];
  attachments: Attachment[];
  comments: Comment[];
  progress_percentage: number;
  progress_notes?: string;
  blockers: Blocker[];
  quality_rating: number;
  completion_rating: number;
  auto_assigned: boolean;
  assignment_score: number;
  assignment_reason?: string;
  manager_approved: boolean;
  approved_by?: string;
  approved_at?: string;
  created_at: string;
  updated_at: string;
}

// 项目问题接口
export interface ProjectIssue {
  id: number;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  reporter_id: string;
  assignee_id?: string;
  created_at: string;
  resolved_at?: string;
  resolution?: string;
}

// 会议纪要接口
export interface MeetingNote {
  id: number;
  title: string;
  content: string;
  attendees: string[];
  meeting_date: string;
  created_by: string;
  created_at: string;
}

// 附件接口
export interface Attachment {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  uploaded_by: string;
  uploaded_at: string;
}

// 评论接口
export interface Comment {
  id: number;
  content: string;
  author_id: string;
  created_at: string;
  type: 'comment' | 'status_change' | 'assignment';
}

// 阻塞因素接口
export interface Blocker {
  id: number;
  description: string;
  type: 'dependency' | 'resource' | 'approval';
  severity: 'low' | 'medium' | 'high';
  status: 'active' | 'resolved';
  created_by: string;
  created_at: string;
  resolved_at?: string;
  resolution?: string;
}

// 工作负荷接口
export interface Workload {
  user_id: string;
  username: string;
  total_tasks: number;
  total_estimated_hours: number;
  priority_distribution: Record<string, number>;
  workload_percentage: number;
  max_concurrent_tasks: number;
  efficiency_factor: number;
}

// 分配评分接口
export interface AssignmentScore {
  user_id: string;
  username: string;
  total_score: number;
  time_urgency_score: number;
  technical_difficulty_score: number;
  current_workload_score: number;
  project_priority_score: number;
  skill_matching_score: number;
  completion_rate_score: number;
  workload_percentage: number;
  reason: string;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error_code?: string;
  timestamp: string;
  code: number;
}

// 分页接口
export interface Pagination {
  page: number;
  per_page: number;
  total: number;
  pages: number;
  has_prev: boolean;
  has_next: boolean;
  prev_page?: number;
  next_page?: number;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[];
  pagination: Pagination;
}

// 登录请求接口
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应接口
export interface LoginResponse {
  token: string;
  user: User;
  expires_in: number;
}

// 筛选器接口
export interface FilterOptions {
  search?: string;
  status?: string;
  priority?: string;
  assignee_id?: string;
  project_id?: string;
  creator_id?: string;
  overdue?: boolean;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// 仪表盘统计接口
export interface DashboardStats {
  total_projects: number;
  total_tasks: number;
  active_tasks: number;
  completed_tasks: number;
  overdue_tasks: number;
  this_week_due: number;
  pending_approvals: number;
  completion_rate: number;
  status_distribution: Record<string, number>;
  priority_distribution: Record<string, number>;
}

// 图表数据接口
export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

// 表格列配置接口
export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

// 表单字段接口
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'date' | 'number' | 'checkbox' | 'radio';
  required?: boolean;
  options?: { label: string; value: any }[];
  placeholder?: string;
  rules?: any[];
}
