"""
CAF项目管理API路由
提供CAF项目的增删改查和Excel导入导出接口
"""

import os
from flask import Blueprint, request, jsonify, current_app
from flask_restx import Api, Resource, fields, Namespace
from werkzeug.utils import secure_filename

from ..services.auth_service import token_required, permission_required
from ..services.caf_project_service import CAFProjectService
from ..services.excel_service import ExcelService
from ..models import CAFProject
from ..utils import success_response, error_response, sanitize_filename

# 创建蓝图
caf_projects_bp = Blueprint('caf_projects', __name__)
api = Api(caf_projects_bp, doc='/caf_projects/docs/', title='CAF项目管理API', description='CAF项目管理相关接口')

# 创建命名空间
caf_projects_ns = Namespace('caf_projects', description='CAF项目管理')
api.add_namespace(caf_projects_ns)

# 定义请求模型
project_model = api.model('CAFProject', {
    'project_name': fields.String(required=True, description='项目名称'),
    'project_code': fields.String(description='项目编号'),
    'caf_code': fields.String(description='CAF编码'),
    'tmt_code': fields.String(description='TMT编码'),
    'chinese_name': fields.String(description='中文名称'),
    'english_name': fields.String(description='英文名称'),
    'technical_lead': fields.String(required=True, description='技术负责人'),
    'technical_manager': fields.String(description='技术经理'),
    'stage': fields.String(description='阶段'),
    'project_start_date': fields.String(description='立项时间'),
    'fatigue_test_conditions': fields.String(description='疲劳试验条件'),
    'cbc_status': fields.String(description='CBC状态'),
    'drawing_code_version': fields.String(description='图纸编码与版本'),
    'drawing_status': fields.String(description='方案图纸状态'),
    'ttp_code_version': fields.String(description='TTP编码与版本'),
    'ttp_status': fields.String(description='TTP状态'),
    'rtp_code_version': fields.String(description='RTP编码与版本'),
    'rtp_status': fields.String(description='RTP状态'),
    'ttr_code_version': fields.String(description='TTR编码与版本'),
    'ttr_status': fields.String(description='TTR状态'),
    'description': fields.String(description='说明')
})


@caf_projects_ns.route('')
class CAFProjectsResource(Resource):
    """CAF项目列表"""
    
    @token_required
    def get(self):
        """获取CAF项目列表"""
        try:
            # 获取查询参数
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
            sort_by = request.args.get('sort_by', 'sequence_number')
            sort_order = request.args.get('sort_order', 'asc')
            
            # 获取筛选参数
            filters = {}
            if request.args.get('technical_lead'):
                filters['technical_lead'] = request.args.get('technical_lead')
            if request.args.get('stage'):
                filters['stage'] = request.args.get('stage')
            if request.args.get('project_name'):
                filters['project_name'] = request.args.get('project_name')
            
            result = CAFProjectService.get_projects_list(
                page=page,
                per_page=per_page,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order
            )
            
            if result['success']:
                return success_response(
                    message='获取项目列表成功',
                    data={
                        'projects': result['projects'],
                        'pagination': result['pagination']
                    }
                ), 200
            else:
                return error_response(result['message'], result['error_code']), 400
                
        except Exception as e:
            return error_response(f'获取项目列表失败: {str(e)}', 'GET_PROJECTS_ERROR'), 500
    
    @token_required
    @permission_required('project.create')
    @api.expect(project_model)
    def post(self):
        """创建CAF项目"""
        try:
            data = request.get_json()
            
            result = CAFProjectService.create_project(data)
            
            if result['success']:
                return success_response(
                    message=result['message'],
                    data={'project': result['project']}
                ), 201
            else:
                return error_response(result['message'], result['error_code']), 400
                
        except Exception as e:
            return error_response(f'创建项目失败: {str(e)}', 'CREATE_PROJECT_ERROR'), 500


@caf_projects_ns.route('/<string:project_id>')
class CAFProjectResource(Resource):
    """单个CAF项目"""
    
    @token_required
    def get(self, project_id):
        """获取CAF项目详情"""
        try:
            result = CAFProjectService.get_project_detail(project_id)
            
            if result['success']:
                return success_response(
                    message='获取项目详情成功',
                    data={'project': result['project']}
                ), 200
            else:
                return error_response(result['message'], result['error_code']), 404
                
        except Exception as e:
            return error_response(f'获取项目详情失败: {str(e)}', 'GET_PROJECT_DETAIL_ERROR'), 500
    
    @token_required
    @permission_required('project.update')
    @api.expect(project_model)
    def put(self, project_id):
        """更新CAF项目"""
        try:
            data = request.get_json()
            
            result = CAFProjectService.update_project(project_id, data)
            
            if result['success']:
                return success_response(
                    message=result['message'],
                    data={'project': result['project']}
                ), 200
            else:
                return error_response(result['message'], result['error_code']), 400
                
        except Exception as e:
            return error_response(f'更新项目失败: {str(e)}', 'UPDATE_PROJECT_ERROR'), 500
    
    @token_required
    @permission_required('project.delete')
    def delete(self, project_id):
        """删除CAF项目"""
        try:
            result = CAFProjectService.delete_project(project_id)
            
            if result['success']:
                return success_response(message=result['message']), 200
            else:
                return error_response(result['message'], result['error_code']), 404
                
        except Exception as e:
            return error_response(f'删除项目失败: {str(e)}', 'DELETE_PROJECT_ERROR'), 500


@caf_projects_ns.route('/import')
class CAFProjectImportResource(Resource):
    """CAF项目Excel导入"""
    
    @token_required
    @permission_required('data.import')
    def post(self):
        """导入CAF项目Excel文件"""
        try:
            if 'file' not in request.files:
                return error_response('没有上传文件', 'NO_FILE'), 400
            
            file = request.files['file']
            if file.filename == '':
                return error_response('文件名为空', 'EMPTY_FILENAME'), 400
            
            # 保存上传的文件
            filename = secure_filename(file.filename)
            upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
            file.save(upload_path)
            
            try:
                # 导入数据
                result = ExcelService.import_caf_project_data(upload_path)
                
                if result['success']:
                    # 保存到数据库
                    saved_count = 0
                    for project_data in result['projects']:
                        try:
                            project = CAFProject(**project_data)
                            project.save()
                            saved_count += 1
                        except Exception as e:
                            print(f"保存项目失败: {e}")
                    
                    return success_response(
                        message=f'成功导入 {saved_count} 个项目',
                        data={
                            'imported_count': saved_count,
                            'failed_count': result['failed_count'],
                            'failed_rows': result['failed_rows']
                        }
                    ), 200
                else:
                    return error_response(result['message'], result['error_code']), 400
                    
            finally:
                # 清理上传的文件
                if os.path.exists(upload_path):
                    os.remove(upload_path)
                    
        except Exception as e:
            return error_response(f'导入失败: {str(e)}', 'IMPORT_ERROR'), 500


@caf_projects_ns.route('/export')
class CAFProjectExportResource(Resource):
    """CAF项目Excel导出"""
    
    @token_required
    def post(self):
        """导出CAF项目到Excel"""
        try:
            data = request.get_json() or {}
            
            # 获取筛选条件
            filters = data.get('filters', {})
            projects = CAFProject.find_all(filters)
            
            if not projects:
                return error_response('没有可导出的项目数据', 'NO_DATA'), 400
            
            # 导出到Excel
            result = ExcelService.export_caf_projects_to_excel(projects, data.get('export_options', {}))
            
            if result['success']:
                return success_response(
                    message=result['message'],
                    data={
                        'file_name': result['file_name'],
                        'exported_count': result['exported_count']
                    }
                ), 200
            else:
                return error_response(result['message'], result['error_code']), 500
                
        except Exception as e:
            return error_response(f'导出失败: {str(e)}', 'EXPORT_ERROR'), 500


@caf_projects_ns.route('/filters')
class CAFProjectFiltersResource(Resource):
    """CAF项目筛选选项"""
    
    @token_required
    def get(self):
        """获取筛选选项"""
        try:
            result = CAFProjectService.get_filter_options()
            
            if result['success']:
                return success_response(
                    message='获取筛选选项成功',
                    data=result['options']
                ), 200
            else:
                return error_response(result['message'], result['error_code']), 500
                
        except Exception as e:
            return error_response(f'获取筛选选项失败: {str(e)}', 'GET_FILTERS_ERROR'), 500


@caf_projects_ns.route('/statistics')
class CAFProjectStatisticsResource(Resource):
    """CAF项目统计"""
    
    @token_required
    def get(self):
        """获取项目统计信息"""
        try:
            result = CAFProjectService.get_statistics()
            
            if result['success']:
                return success_response(
                    message='获取统计信息成功',
                    data=result['statistics']
                ), 200
            else:
                return error_response(result['message'], result['error_code']), 500
                
        except Exception as e:
            return error_response(f'获取统计信息失败: {str(e)}', 'GET_STATISTICS_ERROR'), 500
