#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导入过程
"""

import sys
import os
import pandas as pd

# 添加backend目录到Python路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

# 设置Flask应用上下文
os.environ['FLASK_ENV'] = 'development'

# 创建Flask应用实例
from backend.app import create_app
app = create_app()

with app.app_context():
    from backend.app.services.excel_service import ExcelService

def debug_import():
    """调试导入过程"""
    file_path = '00 CAF项目进展情况.xlsx'
    
    if not os.path.exists(file_path):
        print(f'Excel文件不存在: {file_path}')
        return
    
    print('开始调试导入过程...')
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path, sheet_name='CAF项目状态')
        print(f'Excel文件读取成功，共 {len(df)} 行，{len(df.columns)} 列')
        
        # 获取配置
        config = ExcelService.get_excel_config()
        print(f'\n配置内容: {config.keys()}')

        caf_field_mapping = config.get('caf_field_mapping', {})
        print(f'CAF字段映射数量: {len(caf_field_mapping)}')

        if not caf_field_mapping:
            # 直接定义映射
            caf_field_mapping = {
                '序号': 'sequence_number',
                '项目名称': 'project_name',
                '项目编号': 'project_code',
                'CAF编码': 'caf_code',
                'TMT编码': 'tmt_code',
                '中文名称': 'chinese_name',
                '英文名称': 'english_name',
                '技术负责人': 'technical_lead',
                '技术经理': 'technical_manager',
                '阶段': 'stage',
                '立项时间': 'project_start_date',
                '疲劳试验条件': 'fatigue_test_conditions',
                'CBC状态': 'cbc_status',
                '图纸编码与版本': 'drawing_code_version',
                '方案图纸状态\n（XX日提交/批准）': 'drawing_status',
                'TTP编码与版本': 'ttp_code_version',
                'TTP状态\n（XX日提交/批准）': 'ttp_status',
                'RTP编码与版本': 'rtp_code_version',
                'RTP状态\n（XX日提交/批准）': 'rtp_status',
                'TTR编码与版本': 'ttr_code_version',
                'TTR状态\n（XX日提交/批准）': 'ttr_status',
                '内部图纸下发时间-技术工程师': 'internal_drawing_release_time',
                'FAI日期': 'fai_date',
                '说明': 'description',
                '10/2CAF现场讨论情况': 'caf_onsite_discussion_1002',
                '项目进展1': 'project_progress_1',
                '项目进展2': 'project_progress_2',
                '项目进展3': 'project_progress_3',
                '问题CAF现场交流-支持/讨论事项--2025-3-19': 'caf_issues_discussion_0319',
                '问题CAF会议-支持/讨论事项--2025-4-14': 'caf_meeting_issues_0414',
                '问题CAF会议-支持/讨论事项--2025-6-16': 'caf_meeting_issues_0616'
            }
            print('使用内置字段映射')

        print('\n配置的字段映射:')
        for excel_col, field_name in caf_field_mapping.items():
            print(f'  "{excel_col}" -> {field_name}')
        
        # 创建列名映射（处理Excel中的实际列名）
        actual_column_mapping = {}
        print('\n实际列名匹配:')
        for excel_col, field_name in caf_field_mapping.items():
            # 查找匹配的实际列名
            found = False
            for actual_col in df.columns:
                # 标准化比较（去除空格和换行符）
                normalized_excel = excel_col.replace('\n', '').replace(' ', '')
                normalized_actual = str(actual_col).replace('\n', '').replace(' ', '')
                
                if normalized_excel == normalized_actual or excel_col == actual_col:
                    actual_column_mapping[actual_col] = field_name
                    print(f'  ✓ "{actual_col}" -> {field_name}')
                    found = True
                    break
            
            if not found:
                print(f'  ✗ "{excel_col}" (未找到匹配)')
        
        print(f'\n成功映射 {len(actual_column_mapping)} 个字段')
        
        # 处理第一行数据作为示例
        if len(df) > 0:
            print('\n第一行数据处理示例:')
            first_row = df.iloc[0]
            project_data = {}
            
            for actual_col, field_name in actual_column_mapping.items():
                value = first_row[actual_col]
                
                # 处理空值
                if pd.isna(value) or value == '' or str(value).strip() == '':
                    project_data[field_name] = None
                    print(f'  {field_name}: None (空值)')
                else:
                    # 处理日期时间字段
                    if 'date' in field_name or 'time' in field_name:
                        if isinstance(value, pd.Timestamp):
                            project_data[field_name] = value.isoformat()
                            print(f'  {field_name}: {value.isoformat()} (日期)')
                        else:
                            project_data[field_name] = str(value)
                            print(f'  {field_name}: {str(value)} (字符串日期)')
                    else:
                        project_data[field_name] = str(value).strip()
                        print(f'  {field_name}: "{str(value).strip()}"')
            
            print(f'\n处理后的项目数据:')
            for key, value in project_data.items():
                if value:
                    print(f'  {key}: {value}')
        
    except Exception as e:
        print(f'调试过程中出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    with app.app_context():
        debug_import()
