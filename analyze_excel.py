#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Excel文件结构
"""

import pandas as pd
import sys
import os

def analyze_excel():
    """分析Excel文件"""
    file_path = '00 CAF项目进展情况.xlsx'
    
    if not os.path.exists(file_path):
        print('Excel文件不存在')
        return
    
    try:
        # 获取所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        print('工作表列表:')
        for i, sheet in enumerate(excel_file.sheet_names):
            print(f'{i+1}. {sheet}')
        
        # 读取CAF项目状态工作表
        target_sheet = None
        if 'CAF项目状态' in excel_file.sheet_names:
            target_sheet = 'CAF项目状态'
        else:
            # 查找包含"CAF"或"项目"的工作表
            for sheet in excel_file.sheet_names:
                if 'CAF' in sheet or '项目' in sheet:
                    target_sheet = sheet
                    break
            
            if target_sheet is None:
                target_sheet = excel_file.sheet_names[0]
        
        print(f'\n正在分析工作表: {target_sheet}')
        df = pd.read_excel(file_path, sheet_name=target_sheet)
        
        print(f'行数: {len(df)}')
        print(f'列数: {len(df.columns)}')
        
        print('\n列名列表:')
        for i, col in enumerate(df.columns):
            print(f'{i+1:2d}. {col}')
        
        print('\n前3行数据预览:')
        # 只显示前10列，避免输出过宽
        display_df = df.head(3)
        if len(df.columns) > 10:
            display_df = display_df.iloc[:, :10]
            print('(仅显示前10列)')
        
        print(display_df.to_string(index=False))
        
        # 分析数据类型
        print('\n数据类型分析:')
        for col in df.columns:
            non_null_count = df[col].count()
            null_count = df[col].isnull().sum()
            dtype = df[col].dtype
            print(f'{col}: {dtype}, 非空值: {non_null_count}, 空值: {null_count}')
            
            # 显示示例值
            sample_values = df[col].dropna().head(2).tolist()
            if sample_values:
                print(f'  示例值: {sample_values}')
        
    except Exception as e:
        print(f'读取Excel文件时出错: {e}')

if __name__ == '__main__':
    analyze_excel()
