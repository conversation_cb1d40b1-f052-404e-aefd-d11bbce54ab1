var e=Object.defineProperty,t=Object.defineProperties,n=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,a=(t,n,r)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r,c=(e,t)=>{for(var n in t||(t={}))o.call(t,n)&&a(e,n,t[n]);if(r)for(var n of r(t))i.call(t,n)&&a(e,n,t[n]);return e},s=(e,r)=>t(e,n(r));import{enableES5 as u}from"immer";export*from"redux";import{default as l,current as f,freeze as d,original as p,isDraft as y}from"immer";import{createSelector as m}from"reselect";import{current as h,isDraft as g}from"immer";import{createSelector as b}from"reselect";var w=(...e)=>{const t=b(...e);return(e,...n)=>t(g(e)?h(e):e,...n)};import{createStore as v,compose as O,applyMiddleware as j,combineReducers as E}from"redux";import{compose as S}from"redux";var A="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?S:S.apply(null,arguments)};function P(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);if(null===t)return!0;let n=t;for(;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window;import k from"redux-thunk";var x=e=>e&&"function"==typeof e.match;function C(e,t){function n(...n){if(t){let r=t(...n);if(!r)throw new Error("prepareAction did not return an object");return c(c({type:e,payload:r.payload},"meta"in r&&{meta:r.meta}),"error"in r&&{error:r.error})}return{type:e,payload:n[0]}}return n.toString=()=>`${e}`,n.type=e,n.match=t=>t.type===e,n}function I(e){return P(e)&&"type"in e}function _(e){return"function"==typeof e&&"type"in e&&x(e)}function M(e){return I(e)&&"string"==typeof e.type&&Object.keys(e).every(T)}function T(e){return["type","payload","error","meta"].indexOf(e)>-1}function q(e){return`${e}`}function D(e={}){return()=>e=>t=>e(t)}import R,{isDraftable as L}from"immer";var $=class extends Array{constructor(...e){super(...e),Object.setPrototypeOf(this,$.prototype)}static get[Symbol.species](){return $}concat(...e){return super.concat.apply(this,e)}prepend(...e){return 1===e.length&&Array.isArray(e[0])?new $(...e[0].concat(this)):new $(...e.concat(this))}},B=class extends Array{constructor(...e){super(...e),Object.setPrototypeOf(this,B.prototype)}static get[Symbol.species](){return B}concat(...e){return super.concat.apply(this,e)}prepend(...e){return 1===e.length&&Array.isArray(e[0])?new B(...e[0].concat(this)):new B(...e.concat(this))}};function N(e){return L(e)?R(e,(()=>{})):e}function V(e){return"object"!=typeof e||null==e||Object.isFrozen(e)}function z(e={}){return()=>e=>t=>e(t)}function X(e){const t=typeof e;return null==e||"string"===t||"boolean"===t||"number"===t||Array.isArray(e)||P(e)}function F(e,t="",n=X,r,o=[],i){let a;if(!n(e))return{keyPath:t||"<root>",value:e};if("object"!=typeof e||null===e)return!1;if(null==i?void 0:i.has(e))return!1;const c=null!=r?r(e):Object.entries(e),s=o.length>0;for(const[e,u]of c){const c=t?t+"."+e:e;if(!s||!o.some((e=>e instanceof RegExp?e.test(c):c===e))){if(!n(u))return{keyPath:c,value:u};if("object"==typeof u&&(a=F(u,c,n,r,o,i),a))return a}}return i&&W(e)&&i.add(e),!1}function W(e){if(!Object.isFrozen(e))return!1;for(const t of Object.values(e))if("object"==typeof t&&null!==t&&!W(t))return!1;return!0}function U(e={}){return()=>e=>t=>e(t)}function K(e={}){const{thunk:t=!0,immutableCheck:n=!0,serializableCheck:r=!0,actionCreatorCheck:o=!0}=e;let i=new $;return t&&i.push("boolean"==typeof t?k:k.withExtraArgument(t.extraArgument)),i}function G(e){const t=function(e){return K(e)},{reducer:n,middleware:r=t(),devTools:o=!0,preloadedState:i,enhancers:a}=e||{};let s;if("function"==typeof n)s=n;else{if(!P(n))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');s=E(n)}let u=r;"function"==typeof u&&(u=u(t));const l=j(...u);let f=O;o&&(f=A(c({trace:!1},"object"==typeof o&&o)));const d=new B(l);let p=d;Array.isArray(a)?p=[l,...a]:"function"==typeof a&&(p=a(d));const y=f(...p);return v(s,i,y)}import J,{isDraft as H,isDraftable as Q}from"immer";function Y(e){const t={},n=[];let r;const o={addCase(e,n){const r="string"==typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in t)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return t[r]=n,o},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(r=e,o)};return e(o),[t,n,r]}function Z(e,t,n=[],r){let o,[i,a,c]="function"==typeof t?Y(t):[t,n,r];if("function"==typeof e)o=()=>N(e());else{const t=N(e);o=()=>t}function s(e=o(),t){let n=[i[t.type],...a.filter((({matcher:e})=>e(t))).map((({reducer:e})=>e))];return 0===n.filter((e=>!!e)).length&&(n=[c]),n.reduce(((e,n)=>{if(n){if(H(e)){const r=n(e,t);return void 0===r?e:r}if(Q(e))return J(e,(e=>n(e,t)));{const r=n(e,t);if(void 0===r){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}}return e}),e)}return s.getInitialState=o,s}function ee(e){const{name:t}=e;if(!t)throw new Error("`name` is a required option for createSlice");const n="function"==typeof e.initialState?e.initialState:N(e.initialState),r=e.reducers||{},o=Object.keys(r),i={},a={},s={};function u(){const[t={},r=[],o]="function"==typeof e.extraReducers?Y(e.extraReducers):[e.extraReducers],i=c(c({},t),a);return Z(n,(e=>{for(let t in i)e.addCase(t,i[t]);for(let t of r)e.addMatcher(t.matcher,t.reducer);o&&e.addDefaultCase(o)}))}let l;return o.forEach((e=>{const n=r[e],o=`${t}/${e}`;let c,u;"reducer"in n?(c=n.reducer,u=n.prepare):c=n,i[e]=c,a[o]=c,s[e]=u?C(o,u):C(o)})),{name:t,reducer:(e,t)=>(l||(l=u()),l(e,t)),actions:s,caseReducers:i,getInitialState:()=>(l||(l=u()),l.getInitialState())}}import te,{isDraft as ne}from"immer";function re(e){const t=oe(((t,n)=>e(n)));return function(e){return t(e,void 0)}}function oe(e){return function(t,n){const r=t=>{M(n)?e(n.payload,t):e(n,t)};return ne(t)?(r(t),t):te(t,r)}}function ie(e,t){return t(e)}function ae(e){return Array.isArray(e)||(e=Object.values(e)),e}function ce(e,t,n){e=ae(e);const r=[],o=[];for(const i of e){const e=ie(i,t);e in n.entities?o.push({id:e,changes:i}):r.push(i)}return[r,o]}function se(e){function t(t,n){const r=ie(t,e);r in n.entities||(n.ids.push(r),n.entities[r]=t)}function n(e,n){e=ae(e);for(const r of e)t(r,n)}function r(t,n){const r=ie(t,e);r in n.entities||n.ids.push(r),n.entities[r]=t}function o(e,t){let n=!1;e.forEach((e=>{e in t.entities&&(delete t.entities[e],n=!0)})),n&&(t.ids=t.ids.filter((e=>e in t.entities)))}function i(t,n){const r={},o={};if(t.forEach((e=>{e.id in n.entities&&(o[e.id]={id:e.id,changes:c(c({},o[e.id]?o[e.id].changes:null),e.changes)})})),(t=Object.values(o)).length>0){const o=t.filter((t=>function(t,n,r){const o=Object.assign({},r.entities[n.id],n.changes),i=ie(o,e),a=i!==n.id;return a&&(t[n.id]=i,delete r.entities[n.id]),r.entities[i]=o,a}(r,t,n))).length>0;o&&(n.ids=Object.keys(n.entities))}}function a(t,r){const[o,a]=ce(t,e,r);i(a,r),n(o,r)}return{removeAll:re((function(e){Object.assign(e,{ids:[],entities:{}})})),addOne:oe(t),addMany:oe(n),setOne:oe(r),setMany:oe((function(e,t){e=ae(e);for(const n of e)r(n,t)})),setAll:oe((function(e,t){e=ae(e),t.ids=[],t.entities={},n(e,t)})),updateOne:oe((function(e,t){return i([e],t)})),updateMany:oe(i),upsertOne:oe((function(e,t){return a([e],t)})),upsertMany:oe(a),removeOne:oe((function(e,t){return o([e],t)})),removeMany:oe(o)}}function ue(e={}){const{selectId:t,sortComparer:n}=c({sortComparer:!1,selectId:e=>e.id},e),r={getInitialState:function(e={}){return Object.assign({ids:[],entities:{}},e)}},o={getSelectors:function(e){const t=e=>e.ids,n=e=>e.entities,r=w(t,n,((e,t)=>e.map((e=>t[e])))),o=(e,t)=>t,i=(e,t)=>e[t],a=w(t,(e=>e.length));if(!e)return{selectIds:t,selectEntities:n,selectAll:r,selectTotal:a,selectById:w(n,o,i)};const c=w(e,n);return{selectIds:w(e,t),selectEntities:c,selectAll:w(e,r),selectTotal:w(e,a),selectById:w(c,o,i)}}},i=n?function(e,t){const{removeOne:n,removeMany:r,removeAll:o}=se(e);function i(t,n){const r=(t=ae(t)).filter((t=>!(ie(t,e)in n.entities)));0!==r.length&&u(r,n)}function a(e,t){0!==(e=ae(e)).length&&u(e,t)}function c(t,n){let r=!1;for(let o of t){const t=n.entities[o.id];if(!t)continue;r=!0,Object.assign(t,o.changes);const i=e(t);o.id!==i&&(delete n.entities[o.id],n.entities[i]=t)}r&&l(n)}function s(t,n){const[r,o]=ce(t,e,n);c(o,n),i(r,n)}function u(t,n){t.forEach((t=>{n.entities[e(t)]=t})),l(n)}function l(n){const r=Object.values(n.entities);r.sort(t);const o=r.map(e),{ids:i}=n;(function(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length&&n<t.length;n++)if(e[n]!==t[n])return!1;return!0})(i,o)||(n.ids=o)}return{removeOne:n,removeMany:r,removeAll:o,addOne:oe((function(e,t){return i([e],t)})),updateOne:oe((function(e,t){return c([e],t)})),upsertOne:oe((function(e,t){return s([e],t)})),setOne:oe((function(e,t){return a([e],t)})),setMany:oe(a),setAll:oe((function(e,t){e=ae(e),t.entities={},t.ids=[],i(e,t)})),addMany:oe(i),updateMany:oe(c),upsertMany:oe(s)}}(t,n):se(t);return c(c(c({selectId:t,sortComparer:n},r),o),i)}var le=(e=21)=>{let t="",n=e;for(;n--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},fe=["name","message","stack","code"],de=class{constructor(e,t){this.payload=e,this.meta=t}},pe=class{constructor(e,t){this.payload=e,this.meta=t}},ye=e=>{if("object"==typeof e&&null!==e){const t={};for(const n of fe)"string"==typeof e[n]&&(t[n]=e[n]);return t}return{message:String(e)}},me=(()=>{function e(e,t,n){const r=C(e+"/fulfilled",((e,t,n,r)=>({payload:e,meta:s(c({},r||{}),{arg:n,requestId:t,requestStatus:"fulfilled"})}))),o=C(e+"/pending",((e,t,n)=>({payload:void 0,meta:s(c({},n||{}),{arg:t,requestId:e,requestStatus:"pending"})}))),i=C(e+"/rejected",((e,t,r,o,i)=>({payload:o,error:(n&&n.serializeError||ye)(e||"Rejected"),meta:s(c({},i||{}),{arg:r,requestId:t,rejectedWithValue:!!o,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}))),a="undefined"!=typeof AbortController?AbortController:class{constructor(){this.signal={aborted:!1,addEventListener(){},dispatchEvent:()=>!1,onabort(){},removeEventListener(){},reason:void 0,throwIfAborted(){}}}abort(){}};return Object.assign((function(e){return(c,s,u)=>{const l=(null==n?void 0:n.idGenerator)?n.idGenerator(e):le(),f=new a;let d,p=!1;function y(e){d=e,f.abort()}const m=async function(){var a,m;let h;try{let i=null==(a=null==n?void 0:n.condition)?void 0:a.call(n,e,{getState:s,extra:u});if(null!==(g=i)&&"object"==typeof g&&"function"==typeof g.then&&(i=await i),!1===i||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};p=!0;const b=new Promise(((e,t)=>f.signal.addEventListener("abort",(()=>t({name:"AbortError",message:d||"Aborted"})))));c(o(l,e,null==(m=null==n?void 0:n.getPendingMeta)?void 0:m.call(n,{requestId:l,arg:e},{getState:s,extra:u}))),h=await Promise.race([b,Promise.resolve(t(e,{dispatch:c,getState:s,extra:u,requestId:l,signal:f.signal,abort:y,rejectWithValue:(e,t)=>new de(e,t),fulfillWithValue:(e,t)=>new pe(e,t)})).then((t=>{if(t instanceof de)throw t;return t instanceof pe?r(t.payload,l,e,t.meta):r(t,l,e)}))])}catch(t){h=t instanceof de?i(null,l,e,t.payload,t.meta):i(t,l,e)}var g;return n&&!n.dispatchConditionRejection&&i.match(h)&&h.meta.condition||c(h),h}();return Object.assign(m,{abort:y,requestId:l,arg:e,unwrap:()=>m.then(he)})}}),{pending:o,rejected:i,fulfilled:r,typePrefix:e})}return e.withTypes=()=>e,e})();function he(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var ge=(e,t)=>x(e)?e.match(t):e(t);function be(...e){return t=>e.some((e=>ge(e,t)))}function we(...e){return t=>e.every((e=>ge(e,t)))}function ve(e,t){if(!e||!e.meta)return!1;const n="string"==typeof e.meta.requestId,r=t.indexOf(e.meta.requestStatus)>-1;return n&&r}function Oe(e){return"function"==typeof e[0]&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function je(...e){return 0===e.length?e=>ve(e,["pending"]):Oe(e)?t=>be(...e.map((e=>e.pending)))(t):je()(e[0])}function Ee(...e){return 0===e.length?e=>ve(e,["rejected"]):Oe(e)?t=>be(...e.map((e=>e.rejected)))(t):Ee()(e[0])}function Se(...e){const t=e=>e&&e.meta&&e.meta.rejectedWithValue;return 0===e.length||Oe(e)?n=>we(Ee(...e),t)(n):Se()(e[0])}function Ae(...e){return 0===e.length?e=>ve(e,["fulfilled"]):Oe(e)?t=>be(...e.map((e=>e.fulfilled)))(t):Ae()(e[0])}function Pe(...e){return 0===e.length?e=>ve(e,["pending","fulfilled","rejected"]):Oe(e)?t=>{const n=[];for(const t of e)n.push(t.pending,t.rejected,t.fulfilled);return be(...n)(t)}:Pe()(e[0])}var ke=(e,t)=>{if("function"!=typeof e)throw new TypeError(`${t} is not a function`)},xe=()=>{},Ce=(e,t=xe)=>(e.catch(t),e),Ie=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),_e=(e,t)=>{const n=e.signal;n.aborted||("reason"in n||Object.defineProperty(n,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},Me=class{constructor(e){this.code=e,this.name="TaskAbortError",this.message=`task cancelled (reason: ${e})`}},Te=e=>{if(e.aborted)throw new Me(e.reason)};function qe(e,t){let n=xe;return new Promise(((r,o)=>{const i=()=>o(new Me(e.reason));e.aborted?i():(n=Ie(e,i),t.finally((()=>n())).then(r,o))})).finally((()=>{n=xe}))}var De=e=>t=>Ce(qe(e,t).then((t=>(Te(e),t)))),Re=e=>{const t=De(e);return e=>t(new Promise((t=>setTimeout(t,e))))},{assign:Le}=Object,$e={},Be="listenerMiddleware",Ne=e=>{let{type:t,actionCreator:n,matcher:r,predicate:o,effect:i}=e;if(t)o=C(t).match;else if(n)t=n.type,o=n.match;else if(r)o=r;else if(!o)throw new Error("Creating or removing a listener requires one of the known fields for matching an action");return ke(i,"options.listener"),{predicate:o,type:t,effect:i}},Ve=e=>{e.pending.forEach((e=>{_e(e,"listener-cancelled")}))},ze=(e,t,n)=>{try{e(t,n)}catch(e){setTimeout((()=>{throw e}),0)}},Xe=C(`${Be}/add`),Fe=C(`${Be}/removeAll`),We=C(`${Be}/remove`),Ue=(...e)=>{console.error(`${Be}/error`,...e)};function Ke(e={}){const t=new Map,{extra:n,onError:r=Ue}=e;ke(r,"onError");const o=e=>{for(const n of Array.from(t.values()))if(e(n))return n},i=e=>{let n=o((t=>t.effect===e.effect));return n||(n=(e=>{const{type:t,predicate:n,effect:r}=Ne(e);return{id:le(),effect:r,type:t,predicate:n,pending:new Set,unsubscribe:()=>{throw new Error("Unsubscribe not initialized")}}})(e)),(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),(null==t?void 0:t.cancelActive)&&Ve(e)}))(n)},a=e=>{const{type:t,effect:n,predicate:r}=Ne(e),i=o((e=>("string"==typeof t?e.type===t:e.predicate===r)&&e.effect===n));return i&&(i.unsubscribe(),e.cancelActive&&Ve(i)),!!i},c=async(e,o,a,c)=>{const s=new AbortController,u=((e,t)=>(n,r)=>Ce((async(n,r)=>{Te(t);let o=()=>{};const i=[new Promise(((t,r)=>{let i=e({predicate:n,effect:(e,n)=>{n.unsubscribe(),t([e,n.getState(),n.getOriginalState()])}});o=()=>{i(),r()}}))];null!=r&&i.push(new Promise((e=>setTimeout(e,r,null))));try{const e=await qe(t,Promise.race(i));return Te(t),e}finally{o()}})(n,r)))(i,s.signal),l=[];try{e.pending.add(s),await Promise.resolve(e.effect(o,Le({},a,{getOriginalState:c,condition:(e,t)=>u(e,t).then(Boolean),take:u,delay:Re(s.signal),pause:De(s.signal),extra:n,signal:s.signal,fork:(f=s.signal,d=l,(e,t)=>{ke(e,"taskExecutor");const n=new AbortController;var r;r=n,Ie(f,(()=>_e(r,f.reason)));const o=(async(t,r)=>{try{return await Promise.resolve(),{status:"ok",value:await(async()=>{Te(f),Te(n.signal);const t=await e({pause:De(n.signal),delay:Re(n.signal),signal:n.signal});return Te(n.signal),t})()}}catch(e){return{status:e instanceof Me?"cancelled":"rejected",error:e}}finally{null==r||r()}})(0,(()=>_e(n,"task-completed")));return(null==t?void 0:t.autoJoin)&&d.push(o),{result:De(f)(o),cancel(){_e(n,"task-cancelled")}}}),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach(((e,t,n)=>{e!==s&&(_e(e,"listener-cancelled"),n.delete(e))}))}})))}catch(e){e instanceof Me||ze(r,e,{raisedBy:"effect"})}finally{await Promise.allSettled(l),_e(s,"listener-completed"),e.pending.delete(s)}var f,d},s=(e=>()=>{e.forEach(Ve),e.clear()})(t);return{middleware:e=>n=>o=>{if(!I(o))return n(o);if(Xe.match(o))return i(o.payload);if(Fe.match(o))return void s();if(We.match(o))return a(o.payload);let u=e.getState();const l=()=>{if(u===$e)throw new Error(`${Be}: getOriginalState can only be called synchronously`);return u};let f;try{if(f=n(o),t.size>0){let n=e.getState();const i=Array.from(t.values());for(let t of i){let i=!1;try{i=t.predicate(o,n,u)}catch(e){i=!1,ze(r,e,{raisedBy:"predicate"})}i&&c(t,o,e,l)}}}finally{u=$e}return f},startListening:i,stopListening:a,clearListeners:s}}var Ge,Je="RTK_autoBatch",He=()=>e=>({payload:e,meta:{RTK_autoBatch:!0}}),Qe="function"==typeof queueMicrotask?queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis):e=>(Ge||(Ge=Promise.resolve())).then(e).catch((e=>setTimeout((()=>{throw e}),0))),Ye=e=>t=>{setTimeout(t,e)},Ze="undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Ye(10),et=(e={type:"raf"})=>t=>(...n)=>{const r=t(...n);let o=!0,i=!1,a=!1;const c=new Set,s="tick"===e.type?Qe:"raf"===e.type?Ze:"callback"===e.type?e.queueNotification:Ye(e.timeout),u=()=>{a=!1,i&&(i=!1,c.forEach((e=>e())))};return Object.assign({},r,{subscribe(e){const t=r.subscribe((()=>o&&e()));return c.add(e),()=>{t(),c.delete(e)}},dispatch(e){var t;try{return o=!(null==(t=null==e?void 0:e.meta)?void 0:t.RTK_autoBatch),i=!o,i&&(a||(a=!0,s(u))),r.dispatch(e)}finally{o=!0}}})};u();export{B as EnhancerArray,$ as MiddlewareArray,Je as SHOULD_AUTOBATCH,Me as TaskAbortError,Xe as addListener,et as autoBatchEnhancer,Fe as clearAllListeners,G as configureStore,C as createAction,D as createActionCreatorInvariantMiddleware,me as createAsyncThunk,w as createDraftSafeSelector,ue as createEntityAdapter,z as createImmutableStateInvariantMiddleware,Ke as createListenerMiddleware,l as createNextState,Z as createReducer,m as createSelector,U as createSerializableStateInvariantMiddleware,ee as createSlice,f as current,F as findNonSerializableValue,d as freeze,K as getDefaultMiddleware,q as getType,I as isAction,_ as isActionCreator,we as isAllOf,be as isAnyOf,Pe as isAsyncThunkAction,y as isDraft,M as isFluxStandardAction,Ae as isFulfilled,V as isImmutableDefault,je as isPending,X as isPlain,P as isPlainObject,Ee as isRejected,Se as isRejectedWithValue,ye as miniSerializeError,le as nanoid,p as original,He as prepareAutoBatched,We as removeListener,he as unwrapResult};
//# sourceMappingURL=redux-toolkit.modern.production.min.js.map