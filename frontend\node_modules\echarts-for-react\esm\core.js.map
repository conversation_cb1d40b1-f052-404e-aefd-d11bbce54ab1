{"version": 3, "file": "core.js", "sourceRoot": "", "sources": ["../src/core.tsx"], "names": [], "mappings": ";AACA,OAAO,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AAC7C,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAC1C,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,QAAQ,EAAE,MAAM,oBAAoB,CAAC;AAC9C,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAG5C;;GAEG;AACH;IAA8C,oCAAgC;IAgB5E,0BAAY,KAAwB;QAApC,YACE,kBAAM,KAAK,CAAC,SAKb;QAHC,KAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC7B,KAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAChB,KAAI,CAAC,eAAe,GAAG,IAAI,CAAC;;IAC9B,CAAC;IAED,4CAAiB,GAAjB;QACE,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED,SAAS;IACT,6CAAkB,GAAlB,UAAmB,SAA4B;QAC7C;;;WAGG;QACK,IAAA,eAAe,GAAK,IAAI,CAAC,KAAK,gBAAf,CAAgB;QACvC,IAAI,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1E,OAAO;SACR;QAED,6BAA6B;QAC7B,kBAAkB;QAClB,iBAAiB;QACjB,gDAAgD;QAChD,IACE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YAC3C,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACzC,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EACjD;YACA,IAAI,CAAC,OAAO,EAAE,CAAC;YAEf,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,KAAK;YAC9B,OAAO;SACR;QAED,mDAAmD;QACnD,IAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;QACtF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;YACnE,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC5B;QAED;;WAEG;QACH,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YACtG,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;IACH,CAAC;IAED,+CAAoB,GAApB;QACE,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,6CAAkB,GAAzB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnH,CAAC;IAED;;OAEG;IACK,kCAAO,GAAf;QACE,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI;gBACF,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACjB;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB;YACD,2BAA2B;YAC3B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAChC;IACH,CAAC;IAED;;OAEG;IACK,2CAAgB,GAAxB;QAAA,iBAkBC;QAjBO,IAAA,KAA6B,IAAI,CAAC,KAAK,EAArC,QAAQ,cAAA,EAAE,YAAY,kBAAe,CAAC;QAE9C,0BAA0B;QAC1B,IAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEnD,iBAAiB;QACjB,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;QAEjD,oBAAoB;QACpB,IAAI,UAAU,CAAC,YAAY,CAAC;YAAE,YAAY,CAAC,eAAe,CAAC,CAAC;QAE5D,eAAe;QACf,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACb,KAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAED,kBAAkB;IACV,qCAAU,GAAlB,UAAmB,QAAQ,EAAE,MAAqC;QAChE,SAAS,UAAU,CAAC,SAAiB,EAAE,IAAc;YACnD,4CAA4C;YAC5C,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC3C,gBAAgB;gBAChB,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,KAAK;oBAC3B,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC;aACJ;QACH,CAAC;QAED,gBAAgB;QAChB,KAAK,IAAM,SAAS,IAAI,MAAM,EAAE;YAC9B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;gBAC3D,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;aAC1C;SACF;IACH,CAAC;IAED;;OAEG;IACK,8CAAmB,GAA3B;QACQ,IAAA,KAAsF,IAAI,CAAC,KAAK,EAA9F,MAAM,YAAA,EAAE,gBAAgB,EAAhB,QAAQ,mBAAG,KAAK,KAAA,EAAE,kBAAkB,EAAlB,UAAU,mBAAG,KAAK,KAAA,EAAE,WAAW,iBAAA,EAAE,qBAAoB,EAApB,aAAa,mBAAG,IAAI,KAAe,CAAC;QACvG,uCAAuC;QACvC,IAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACjD,4BAA4B;QAC5B,cAAc,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACvD,sBAAsB;QACtB,IAAI,WAAW;YAAE,cAAc,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;;YACtD,cAAc,CAAC,WAAW,EAAE,CAAC;QAElC,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,iCAAM,GAAd;QACE,4BAA4B;QAC5B,IAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAElD,4DAA4D;QAC5D,wFAAwF;QACxF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI;gBACF,eAAe,CAAC,MAAM,EAAE,CAAC;aAC1B;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB;SACF;QAED,sCAAsC;QACtC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED,iCAAM,GAAN;QAAA,iBAcC;QAbO,IAAA,KAA4B,IAAI,CAAC,KAAK,EAApC,KAAK,WAAA,EAAE,iBAAc,EAAd,SAAS,mBAAG,EAAE,KAAe,CAAC;QAC7C,uBAAuB;QACvB,IAAM,QAAQ,cAAK,MAAM,EAAE,GAAG,IAAK,KAAK,CAAE,CAAC;QAE3C,OAAO,CACL,6BACE,GAAG,EAAE,UAAC,CAAc;gBAClB,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;YACf,CAAC,EACD,KAAK,EAAE,QAAQ,EACf,SAAS,EAAE,uBAAqB,SAAW,GAC3C,CACH,CAAC;IACJ,CAAC;IACH,uBAAC;AAAD,CAAC,AA/LD,CAA8C,aAAa,GA+L1D", "sourcesContent": ["import type { ECharts } from 'echarts';\nimport React, { PureComponent } from 'react';\nimport { bind, clear } from 'size-sensor';\nimport { pick } from './helper/pick';\nimport { isFunction } from './helper/is-function';\nimport { isString } from './helper/is-string';\nimport { isEqual } from './helper/is-equal';\nimport { EChartsReactProps, EChartsInstance } from './types';\n\n/**\n * core component for echarts binding\n */\nexport default class EChartsReactCore extends PureComponent<EChartsReactProps> {\n  /**\n   * echarts render container\n   */\n  public ele: HTMLElement;\n\n  /**\n   * if this is the first time we are resizing\n   */\n  private isInitialResize: boolean;\n\n  /**\n   * echarts library entry\n   */\n  protected echarts: any;\n\n  constructor(props: EChartsReactProps) {\n    super(props);\n\n    this.echarts = props.echarts;\n    this.ele = null;\n    this.isInitialResize = true;\n  }\n\n  componentDidMount() {\n    this.renderNewEcharts();\n  }\n\n  // update\n  componentDidUpdate(prevProps: EChartsReactProps) {\n    /**\n     * if shouldSetOption return false, then return, not update echarts options\n     * default is true\n     */\n    const { shouldSetOption } = this.props;\n    if (isFunction(shouldSetOption) && !shouldSetOption(prevProps, this.props)) {\n      return;\n    }\n\n    // 以下属性修改的时候，需要 dispose 之后再新建\n    // 1. 切换 theme 的时候\n    // 2. 修改 opts 的时候\n    // 3. 修改 onEvents 的时候，这样可以取消所有之前绑定的事件 issue #151\n    if (\n      !isEqual(prevProps.theme, this.props.theme) ||\n      !isEqual(prevProps.opts, this.props.opts) ||\n      !isEqual(prevProps.onEvents, this.props.onEvents)\n    ) {\n      this.dispose();\n\n      this.renderNewEcharts(); // 重建\n      return;\n    }\n\n    // when these props are not isEqual, update echarts\n    const pickKeys = ['option', 'notMerge', 'lazyUpdate', 'showLoading', 'loadingOption'];\n    if (!isEqual(pick(this.props, pickKeys), pick(prevProps, pickKeys))) {\n      this.updateEChartsOption();\n    }\n\n    /**\n     * when style or class name updated, change size.\n     */\n    if (!isEqual(prevProps.style, this.props.style) || !isEqual(prevProps.className, this.props.className)) {\n      this.resize();\n    }\n  }\n\n  componentWillUnmount() {\n    this.dispose();\n  }\n\n  /**\n   * return the echart object\n   * 1. if exist, return the existed instance\n   * 2. or new one instance\n   */\n  public getEchartsInstance(): ECharts {\n    return this.echarts.getInstanceByDom(this.ele) || this.echarts.init(this.ele, this.props.theme, this.props.opts);\n  }\n\n  /**\n   * dispose echarts and clear size-sensor\n   */\n  private dispose() {\n    if (this.ele) {\n      try {\n        clear(this.ele);\n      } catch (e) {\n        console.warn(e);\n      }\n      // dispose echarts instance\n      this.echarts.dispose(this.ele);\n    }\n  }\n\n  /**\n   * render a new echarts instance\n   */\n  private renderNewEcharts() {\n    const { onEvents, onChartReady } = this.props;\n\n    // 1. new echarts instance\n    const echartsInstance = this.updateEChartsOption();\n\n    // 2. bind events\n    this.bindEvents(echartsInstance, onEvents || {});\n\n    // 3. on chart ready\n    if (isFunction(onChartReady)) onChartReady(echartsInstance);\n\n    // 4. on resize\n    if (this.ele) {\n      bind(this.ele, () => {\n        this.resize();\n      });\n    }\n  }\n\n  // bind the events\n  private bindEvents(instance, events: EChartsReactProps['onEvents']) {\n    function _bindEvent(eventName: string, func: Function) {\n      // ignore the event config which not satisfy\n      if (isString(eventName) && isFunction(func)) {\n        // binding event\n        instance.on(eventName, (param) => {\n          func(param, instance);\n        });\n      }\n    }\n\n    // loop and bind\n    for (const eventName in events) {\n      if (Object.prototype.hasOwnProperty.call(events, eventName)) {\n        _bindEvent(eventName, events[eventName]);\n      }\n    }\n  }\n\n  /**\n   * render the echarts\n   */\n  private updateEChartsOption(): EChartsInstance {\n    const { option, notMerge = false, lazyUpdate = false, showLoading, loadingOption = null } = this.props;\n    // 1. get or initial the echarts object\n    const echartInstance = this.getEchartsInstance();\n    // 2. set the echarts option\n    echartInstance.setOption(option, notMerge, lazyUpdate);\n    // 3. set loading mask\n    if (showLoading) echartInstance.showLoading(loadingOption);\n    else echartInstance.hideLoading();\n\n    return echartInstance;\n  }\n\n  /**\n   * resize wrapper\n   */\n  private resize() {\n    // 1. get the echarts object\n    const echartsInstance = this.getEchartsInstance();\n\n    // 2. call echarts instance resize if not the initial resize\n    // resize should not happen on first render as it will cancel initial echarts animations\n    if (!this.isInitialResize) {\n      try {\n        echartsInstance.resize();\n      } catch (e) {\n        console.warn(e);\n      }\n    }\n\n    // 3. update variable for future calls\n    this.isInitialResize = false;\n  }\n\n  render(): JSX.Element {\n    const { style, className = '' } = this.props;\n    // default height = 300\n    const newStyle = { height: 300, ...style };\n\n    return (\n      <div\n        ref={(e: HTMLElement) => {\n          this.ele = e;\n        }}\n        style={newStyle}\n        className={`echarts-for-react ${className}`}\n      />\n    );\n  }\n}\n"]}