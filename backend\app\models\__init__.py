"""
模型包初始化文件
导出所有模型类和相关枚举
"""

from .base import BaseModel, DataManager
from .user import User, UserRole, UserStatus, DEFAULT_PERMISSIONS
from .project import Project, ProjectPriority, ProjectStatus, ProjectStage
from .task import Task, TaskStatus, TaskPriority, TaskType
from .caf_project import CAFProject

__all__ = [
    # 基础类
    'BaseModel',
    'DataManager',

    # 用户相关
    'User',
    'UserRole',
    'UserStatus',
    'DEFAULT_PERMISSIONS',

    # 项目相关
    'Project',
    'ProjectPriority',
    'ProjectStatus',
    'ProjectStage',

    # 任务相关
    'Task',
    'TaskStatus',
    'TaskPriority',
    'TaskType',

    # CAF项目相关
    'CAFProject',
]


def init_default_data():
    """初始化默认数据"""
    
    # 创建默认超级管理员
    admin_users = User.find_by_role(UserRole.SUPER_ADMIN.value)
    if not admin_users:
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='系统管理员',
            role=UserRole.SUPER_ADMIN.value,
            status=UserStatus.ACTIVE.value,
            permissions=DEFAULT_PERMISSIONS[UserRole.SUPER_ADMIN.value],
            department='信息技术部',
            position='系统管理员',
            skills=['系统管理', '数据分析'],
            experience_level=5,
            max_concurrent_tasks=10
        )
        admin.set_password('admin123')
        admin.save()
        print("已创建默认超级管理员账户: admin/admin123")
    
    # 创建示例用户数据
    sample_users = [
        {
            'username': 'director_zhang',
            'email': '<EMAIL>',
            'full_name': '张主任',
            'role': UserRole.DIRECTOR.value,
            'department': '技术部',
            'position': '技术主任',
            'skills': ['项目管理', '技术架构', '团队协调'],
            'experience_level': 5,
            'max_concurrent_tasks': 8
        },
        {
            'username': 'manager_li',
            'email': '<EMAIL>',
            'full_name': '李部长',
            'role': UserRole.MANAGER.value,
            'department': '设计部',
            'position': '设计部长',
            'skills': ['设计管理', '质量控制', '流程优化'],
            'experience_level': 4,
            'max_concurrent_tasks': 6
        },
        {
            'username': 'designer_wang',
            'email': '<EMAIL>',
            'full_name': '王设计师',
            'role': UserRole.DESIGNER.value,
            'department': '设计部',
            'position': '高级设计师',
            'skills': ['CAD设计', '3D建模', '技术绘图'],
            'experience_level': 3,
            'max_concurrent_tasks': 5
        },
        {
            'username': 'designer_chen',
            'email': '<EMAIL>',
            'full_name': '陈设计师',
            'role': UserRole.DESIGNER.value,
            'department': '设计部',
            'position': '设计师',
            'skills': ['机械设计', '结构分析', 'SolidWorks'],
            'experience_level': 2,
            'max_concurrent_tasks': 4
        }
    ]
    
    for user_data in sample_users:
        existing_user = User.find_by_username(user_data['username'])
        if not existing_user:
            user = User(**user_data)
            user.status = UserStatus.ACTIVE.value
            user.permissions = DEFAULT_PERMISSIONS.get(user.role, [])
            user.set_password('123456')  # 默认密码
            user.save()
    
    print("已创建示例用户数据")
    
    # 创建示例项目数据
    sample_projects = [
        {
            'name': 'CAF-2024-001 智能制造系统',
            'code': 'PRJ-001',
            'caf_code': 'CAF-2024-001',
            'tmt_code': 'TMT-001',
            'description': '智能制造系统设计与开发项目',
            'priority': ProjectPriority.P1.value,
            'status': ProjectStatus.IN_PROGRESS.value,
            'stage': ProjectStage.DESIGN.value,
            'difficulty_level': 4,
            'estimated_hours': 200,
            'deadline': '2024-12-31T23:59:59',
            'department': '技术部',
            'category': '智能制造'
        },
        {
            'name': 'CAF-2024-002 自动化生产线',
            'code': 'PRJ-002',
            'caf_code': 'CAF-2024-002',
            'tmt_code': 'TMT-002',
            'description': '自动化生产线改造项目',
            'priority': ProjectPriority.P2.value,
            'status': ProjectStatus.PLANNING.value,
            'stage': ProjectStage.INITIATION.value,
            'difficulty_level': 3,
            'estimated_hours': 150,
            'deadline': '2025-03-31T23:59:59',
            'department': '设计部',
            'category': '自动化'
        }
    ]
    
    for project_data in sample_projects:
        existing_project = Project.find_by(code=project_data['code'])
        if not existing_project:
            # 分配项目负责人
            managers = User.find_by_role(UserRole.MANAGER.value)
            if managers:
                project_data['owner_id'] = managers[0].id
                project_data['technical_lead_id'] = managers[0].id
            
            project = Project(**project_data)
            project.save()
    
    print("已创建示例项目数据")
    
    # 创建示例任务数据
    projects = Project.find_all()
    designers = User.find_by_role(UserRole.DESIGNER.value)
    
    if projects and designers:
        sample_tasks = [
            {
                'title': '系统架构设计',
                'description': '设计智能制造系统的整体架构',
                'project_id': projects[0].id,
                'type': TaskType.DESIGN.value,
                'priority': TaskPriority.P1.value,
                'difficulty_level': 4,
                'estimated_hours': 40,
                'required_skills': ['系统设计', '架构设计'],
                'due_date': '2024-11-30T23:59:59'
            },
            {
                'title': '界面原型设计',
                'description': '设计用户界面原型和交互流程',
                'project_id': projects[0].id,
                'type': TaskType.DESIGN.value,
                'priority': TaskPriority.P2.value,
                'difficulty_level': 3,
                'estimated_hours': 24,
                'required_skills': ['UI设计', '原型设计'],
                'due_date': '2024-12-15T23:59:59'
            }
        ]
        
        for i, task_data in enumerate(sample_tasks):
            existing_task = Task.find_by(title=task_data['title'])
            if not existing_task:
                task_data['creator_id'] = designers[0].id
                task_data['assignee_id'] = designers[i % len(designers)].id
                task = Task(**task_data)
                task.save()
        
        print("已创建示例任务数据")


def get_system_stats():
    """获取系统统计信息"""
    return {
        'users': len(User.find_all()),
        'projects': len(Project.find_all()),
        'tasks': len(Task.find_all()),
        'active_users': len(User.get_active_users()),
        'active_projects': len(Project.find_by_status(ProjectStatus.IN_PROGRESS.value)),
        'pending_tasks': len(Task.find_by_status(TaskStatus.PENDING.value))
    }
