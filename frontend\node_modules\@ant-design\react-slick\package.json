{"name": "@ant-design/react-slick", "version": "1.1.2", "description": " React port of slick carousel", "main": "./lib", "module": "./es", "types": "./types.d.ts", "files": ["dist", "lib", "es", "types.d.ts"], "scripts": {"start": "NODE_OPTIONS=--openssl-legacy-provider gulp server", "demo": "SINGLE_DEMO=true DEV_PORT=8000 NODE_OPTIONS=--openssl-legacy-provider gulp server", "build-dev": "gulp clean && gulp copy && webpack", "es": "ESMODULES=true babel ./src --out-dir ./es", "lib": "babel ./src --out-dir ./lib", "build": "NODE_OPTIONS=--openssl-legacy-provider npm run lib && NODE_OPTIONS=--openssl-legacy-provider npm run es && NODE_OPTIONS=--openssl-legacy-provider gulp dist", "prepublishOnly": "npm run build && np --no-cleanup --no-publish", "lint": "eslint src", "gen": "node docs/scripts/generateExampleConfigs.js && node docs/scripts/generateExamples.js && xdg-open docs/jquery.html", "precommit": "lint-staged", "test": "jest", "test-watch": "jest --watch", "clear-jest": "jest --clear<PERSON>ache"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ant-design/react-slick"}, "keywords": ["slick", "carousel", "Image slider", "orbit", "slider", "react-component"], "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-transform-runtime": "^7.10.4", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^14.3.0", "autoprefixer": "^7.1.2", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.8.0", "babel-loader": "^8.0.4", "babel-preset-airbnb": "^5.0.0", "css-loader": "^5.0.0", "deepmerge": "^1.1.0", "del": "^2.2.2", "es5-shim": "^4.5.9", "eslint": "^8.4.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-react": "^7.27.1", "express": "^4.14.0", "foundation-apps": "^1.2.0", "gulp": "^4.0.0", "husky": "^0.14.3", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "jquery": "^3.7.1", "js-beautify": "^1.7.5", "json-loader": "^0.5.4", "lint-staged": "^12.1.2", "np": "^8.0.4", "opn": "^5.4.0", "postcss-loader": "^1.3.3", "prettier": "^2.1.2", "raf": "^3.4.0", "react": "^18.0.0", "react-dom": "^18.0.0", "regenerator-runtime": "^0.14.1", "sinon": "^2.1.0", "slick-carousel": "^1.8.1", "style-loader": "^0.16.1", "uglifyjs-webpack-plugin": "^2.0.1", "webpack": "^4.21.0", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.9", "why-did-you-update": "^0.1.1"}, "dependencies": {"@babel/runtime": "^7.10.4", "classnames": "^2.2.5", "json2mq": "^0.2.0", "resize-observer-polyfill": "^1.5.1", "throttle-debounce": "^5.0.0"}, "peerDependencies": {"react": ">=16.9.0"}, "lint-staged": {"*.{js,json,md}": ["prettier --write", "git add"]}, "npmName": "@ant-design/react-slick", "npmFileMap": [{"basePath": "/dist/", "files": ["*.js"]}], "bugs": {"url": "https://github.com/ant-design/react-slick/issues"}, "homepage": "https://react-slick.neostack.com", "collective": {"type": "opencollective", "url": "https://opencollective.com/react-slick", "logo": "https://opencollective.com/opencollective/logo.txt"}, "publishConfig": {"access": "public"}}