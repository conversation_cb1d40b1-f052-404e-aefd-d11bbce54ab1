#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel列名和数据
"""

import pandas as pd
import os

def check_excel_columns():
    """检查Excel列名和数据"""
    file_path = '00 CAF项目进展情况.xlsx'
    
    if not os.path.exists(file_path):
        print(f'Excel文件不存在: {file_path}')
        return
    
    try:
        df = pd.read_excel(file_path, sheet_name='CAF项目状态')
        
        print('Excel列名:')
        for i, col in enumerate(df.columns):
            print(f'{i+1:2d}. "{col}"')
        
        print('\n第一行数据:')
        first_row = df.iloc[0]
        for col in df.columns:
            value = first_row[col]
            if pd.notna(value) and str(value).strip():
                print(f'{col}: {value}')
        
        print('\n字段映射检查:')
        # 检查当前的字段映射
        caf_field_mapping = {
            # 基础信息字段
            '序号': 'sequence_number',
            '项目名称': 'project_name',
            '项目编号': 'project_code',
            'CAF编码': 'caf_code',
            'TMT编码': 'tmt_code',
            '中文名称': 'chinese_name',
            '英文名称': 'english_name',
            '技术负责人': 'technical_lead',
            '技术经理': 'technical_manager',
            '阶段': 'stage',
            '立项时间': 'project_start_date',
            '疲劳试验条件': 'fatigue_test_conditions',
        }
        
        for excel_col, field_name in caf_field_mapping.items():
            if excel_col in df.columns:
                print(f'✓ {excel_col} -> {field_name}')
            else:
                print(f'✗ {excel_col} (不存在)')
        
        print('\n未映射的列:')
        mapped_cols = set(caf_field_mapping.keys())
        for col in df.columns:
            if col not in mapped_cols:
                print(f'  {col}')
                
    except Exception as e:
        print(f'检查Excel文件时出错: {e}')

if __name__ == '__main__':
    check_excel_columns()
