#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动后端Flask服务器
"""

import os
import sys

# 添加backend目录到Python路径
backend_path = os.path.join(os.getcwd(), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

# 设置环境变量
os.environ['FLASK_ENV'] = 'development'
os.environ['FLASK_DEBUG'] = '1'

# 导入并创建Flask应用
from backend.app import create_app

if __name__ == '__main__':
    app = create_app()
    print('启动Flask服务器...')
    print('访问地址: http://localhost:5001')
    print('API文档: http://localhost:5001/api/caf_projects/docs/')
    app.run(host='0.0.0.0', port=5001, debug=True)
