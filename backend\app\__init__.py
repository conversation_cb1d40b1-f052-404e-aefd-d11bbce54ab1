"""
Flask应用工厂
创建和配置Flask应用实例
"""

import os
from flask import Flask
from flask_cors import CORS
from flask_restx import Api

from .config import config


def create_app(config_name=None):
    """应用工厂函数"""
    
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化配置
    config[config_name].init_app(app)
    
    # 初始化CORS
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # 注册蓝图
    register_blueprints(app)
    
    # 初始化默认数据
    with app.app_context():
        from .models import init_default_data
        init_default_data()
    
    return app


def register_blueprints(app):
    """注册蓝图"""
    
    # 认证API
    from .api.auth import auth_bp
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    
    # 用户管理API
    from .api.users import users_bp
    app.register_blueprint(users_bp, url_prefix='/api/users')
    
    # 项目管理API
    from .api.projects import projects_bp
    app.register_blueprint(projects_bp, url_prefix='/api/projects')
    
    # 任务管理API
    from .api.tasks import tasks_bp
    app.register_blueprint(tasks_bp, url_prefix='/api/tasks')
    
    # 仪表盘API
    from .api.dashboard import dashboard_bp
    app.register_blueprint(dashboard_bp, url_prefix='/api/dashboard')
    
    # Excel处理API
    from .api.excel import excel_bp
    app.register_blueprint(excel_bp, url_prefix='/api/excel')

    # CAF项目管理API
    from .api.caf_projects import caf_projects_bp
    app.register_blueprint(caf_projects_bp, url_prefix='/api/caf_projects')


# 创建默认应用实例
app = create_app()
