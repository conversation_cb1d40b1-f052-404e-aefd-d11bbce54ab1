import { api } from './index'
import type { CAFProject, CAFProjectDetail } from '@/types'

export interface CAFProjectsListParams {
  page?: number
  per_page?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  technical_lead?: string
  stage?: string
  project_name?: string
}

export interface CAFProjectsListResponse {
  projects: CAFProject[]
  pagination: {
    page: number
    per_page: number
    total: number
    pages: number
    has_prev: boolean
    has_next: boolean
  }
}

export interface CAFProjectDetailResponse {
  project: CAFProjectDetail
}

export interface CAFProjectFiltersResponse {
  technical_leads: string[]
  stages: string[]
  difficulty_levels: string[]
}

export interface CAFProjectStatisticsResponse {
  total_projects: number
  stage_distribution: Record<string, number>
  lead_distribution: Record<string, number>
  difficulty_distribution: Record<string, number>
  progress_distribution: Record<string, number>
}

export interface ImportCAFProjectResponse {
  imported_count: number
  failed_count: number
  failed_rows: Array<{
    row: number
    error: string
    data: any
  }>
}

export interface ExportCAFProjectResponse {
  file_name: string
  exported_count: number
}

export const cafProjectsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // 获取CAF项目列表
    getCAFProjects: builder.query<CAFProjectsListResponse, CAFProjectsListParams>({
      query: (params) => ({
        url: '/caf_projects',
        params,
      }),
      providesTags: ['CAFProject'],
    }),

    // 获取CAF项目详情
    getCAFProjectDetail: builder.query<CAFProjectDetailResponse, string>({
      query: (id) => `/caf_projects/${id}`,
      providesTags: (result, error, id) => [{ type: 'CAFProject', id }],
    }),

    // 创建CAF项目
    createCAFProject: builder.mutation<{ project: CAFProject }, Partial<CAFProject>>({
      query: (data) => ({
        url: '/caf_projects',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CAFProject'],
    }),

    // 更新CAF项目
    updateCAFProject: builder.mutation<{ project: CAFProject }, { id: string; data: Partial<CAFProject> }>({
      query: ({ id, data }) => ({
        url: `/caf_projects/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'CAFProject', id }, 'CAFProject'],
    }),

    // 删除CAF项目
    deleteCAFProject: builder.mutation<void, string>({
      query: (id) => ({
        url: `/caf_projects/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['CAFProject'],
    }),

    // 导入CAF项目Excel
    importCAFProjects: builder.mutation<ImportCAFProjectResponse, FormData>({
      query: (formData) => ({
        url: '/caf_projects/import',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['CAFProject'],
    }),

    // 导出CAF项目Excel
    exportCAFProjects: builder.mutation<ExportCAFProjectResponse, { filters?: any; export_options?: any }>({
      query: (data) => ({
        url: '/caf_projects/export',
        method: 'POST',
        body: data,
      }),
    }),

    // 获取筛选选项
    getCAFProjectFilters: builder.query<CAFProjectFiltersResponse, void>({
      query: () => '/caf_projects/filters',
    }),

    // 获取统计信息
    getCAFProjectStatistics: builder.query<CAFProjectStatisticsResponse, void>({
      query: () => '/caf_projects/statistics',
    }),
  }),
})

export const {
  useGetCAFProjectsQuery,
  useGetCAFProjectDetailQuery,
  useCreateCAFProjectMutation,
  useUpdateCAFProjectMutation,
  useDeleteCAFProjectMutation,
  useImportCAFProjectsMutation,
  useExportCAFProjectsMutation,
  useGetCAFProjectFiltersQuery,
  useGetCAFProjectStatisticsQuery,
} = cafProjectsApi
