import React, { useState, useRef } from 'react'
import {
  Typo<PERSON>,
  Button,
  Table,
  Space,
  Input,
  Select,
  Modal,
  Upload,
  message,
  Tooltip,
  Tag,
  Progress,
  Popconfirm,
  Row,
  Col,
  Card,
  Descriptions,
  Divider
} from 'antd'
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  ReloadOutlined,
  FilterOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  DownloadOutlined,
  UserOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UploadProps } from 'antd'

import {
  useGetCAFProjectsQuery,
  useGetCAFProjectDetailQuery,
  useCreateCAFProjectMutation,
  useUpdateCAFProjectMutation,
  useDeleteCAFProjectMutation,
  useImportCAFProjectsMutation,
  useExportCAFProjectsMutation,
  useGetCAFProjectFiltersQuery
} from '@/store/api/cafProjectsApi'
import type { CAFProject, CAFProjectDetail } from '@/types'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select

const Tasks: React.FC = () => {
  // 状态管理
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [sortBy, setSortBy] = useState('sequence_number')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [filters, setFilters] = useState<any>({})
  const [searchText, setSearchText] = useState('')
  const [selectedProject, setSelectedProject] = useState<string | null>(null)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [importModalVisible, setImportModalVisible] = useState(false)
  const [editingProject, setEditingProject] = useState<Partial<CAFProject> | null>(null)

  // API hooks
  const {
    data: projectsData,
    isLoading,
    refetch
  } = useGetCAFProjectsQuery({
    page: currentPage,
    per_page: pageSize,
    sort_by: sortBy,
    sort_order: sortOrder,
    ...filters,
    project_name: searchText || undefined
  })

  const {
    data: projectDetail,
    isLoading: detailLoading
  } = useGetCAFProjectDetailQuery(selectedProject!, {
    skip: !selectedProject
  })

  const { data: filterOptions } = useGetCAFProjectFiltersQuery()

  const [createProject] = useCreateCAFProjectMutation()
  const [updateProject] = useUpdateCAFProjectMutation()
  const [deleteProject] = useDeleteCAFProjectMutation()
  const [importProjects] = useImportCAFProjectsMutation()
  const [exportProjects] = useExportCAFProjectsMutation()

  // 表格列定义（严格按照12个字段）
  const columns: ColumnsType<CAFProject> = [
    {
      title: '序号',
      dataIndex: 'sequence_number',
      key: 'sequence_number',
      width: 80,
      sorter: true,
      render: (value) => value || '-'
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
      width: 150,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <Text strong>{text}</Text>
        </Tooltip>
      )
    },
    {
      title: '项目编号',
      dataIndex: 'project_code',
      key: 'project_code',
      width: 120,
      ellipsis: true,
      render: (value) => value || '-'
    },
    {
      title: 'TMT编码',
      dataIndex: 'tmt_code',
      key: 'tmt_code',
      width: 120,
      ellipsis: true,
      render: (value) => value || '-'
    },
    {
      title: '技术负责人',
      dataIndex: 'technical_lead',
      key: 'technical_lead',
      width: 100,
      render: (text) => (
        <Tag icon={<UserOutlined />} color="blue">
          {text}
        </Tag>
      )
    },
    {
      title: '技术难度',
      dataIndex: 'difficulty_level',
      key: 'difficulty_level',
      width: 100,
      render: (level) => {
        const colorMap: Record<string, string> = {
          '高': 'red',
          '中': 'orange',
          '低': 'green',
          '待评估': 'default'
        }
        return <Tag color={colorMap[level] || 'default'}>{level || '待评估'}</Tag>
      }
    },
    {
      title: '进度',
      dataIndex: 'progress_percentage',
      key: 'progress_percentage',
      width: 120,
      render: (progress) => (
        <Progress
          percent={progress || 0}
          size="small"
          status={progress === 100 ? 'success' : progress > 75 ? 'active' : 'normal'}
        />
      )
    },
    {
      title: '阶段',
      dataIndex: 'stage',
      key: 'stage',
      width: 100,
      render: (stage) => {
        const colorMap: Record<string, string> = {
          '未入协议': 'default',
          '首检': 'processing',
          '试验': 'warning',
          '完成': 'success'
        }
        return <Tag color={colorMap[stage] || 'default'}>{stage || '-'}</Tag>
      }
    },
    {
      title: '立项时间',
      dataIndex: 'project_start_date',
      key: 'project_start_date',
      width: 120,
      render: (date) => {
        if (!date) return '-'
        try {
          return new Date(date).toLocaleDateString('zh-CN')
        } catch {
          return date
        }
      }
    },
    {
      title: '完成时间',
      dataIndex: 'completion_date',
      key: 'completion_date',
      width: 120,
      render: (date) => {
        if (!date) return '-'
        try {
          return new Date(date).toLocaleDateString('zh-CN')
        } catch {
          return date
        }
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record.id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个项目吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 事件处理函数
  const handleViewDetail = (projectId: string) => {
    setSelectedProject(projectId)
    setDetailModalVisible(true)
  }

  const handleEdit = (project: CAFProject) => {
    setEditingProject(project)
    setEditModalVisible(true)
  }

  const handleDelete = async (projectId: string) => {
    try {
      await deleteProject(projectId).unwrap()
      message.success('删除成功')
    } catch (error) {
      message.error('删除失败')
    }
  }

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setCurrentPage(pagination.current)
    setPageSize(pagination.pageSize)

    if (sorter.field) {
      setSortBy(sorter.field)
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc')
    }
  }

  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
    setCurrentPage(1)
  }

  const handleExport = async () => {
    try {
      const result = await exportProjects({
        filters,
        export_options: {
          file_name: `CAF项目数据导出_${new Date().toISOString().split('T')[0]}.xlsx`
        }
      }).unwrap()

      // 触发下载
      const link = document.createElement('a')
      link.href = `/api/excel/download/${result.file_name}`
      link.download = result.file_name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      message.success(`成功导出 ${result.exported_count} 个项目`)
    } catch (error) {
      message.error('导出失败')
    }
  }

  return (
    <div>
      <div className="page-header">
        <div>
          <Title level={2} className="page-title">任务管理</Title>
          <div className="page-description">CAF项目进展情况管理</div>
        </div>
      </div>

      {/* 顶部功能栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Space wrap>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateModalVisible(true)}
              >
                新建任务
              </Button>
              <Button
                icon={<UserOutlined />}
                onClick={() => message.info('项目自动分配功能开发中')}
              >
                项目自动分配
              </Button>
              <Button
                icon={<ImportOutlined />}
                onClick={() => setImportModalVisible(true)}
              >
                导入Excel
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={handleExport}
              >
                导出Excel
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => refetch()}
              >
                刷新
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Select
                placeholder="阶段筛选"
                allowClear
                style={{ width: 120 }}
                onChange={(value) => handleFilterChange('stage', value)}
              >
                {filterOptions?.stages.map(stage => (
                  <Option key={stage} value={stage}>{stage}</Option>
                ))}
              </Select>
              <Select
                placeholder="技术负责人"
                allowClear
                style={{ width: 120 }}
                onChange={(value) => handleFilterChange('technical_lead', value)}
              >
                {filterOptions?.technical_leads.map(lead => (
                  <Option key={lead} value={lead}>{lead}</Option>
                ))}
              </Select>
              <Search
                placeholder="搜索项目名称"
                allowClear
                style={{ width: 200 }}
                onSearch={handleSearch}
              />
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 主表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={projectsData?.projects || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: projectsData?.pagination.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
          size="small"
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="项目详情 - 完整信息"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1000}
        style={{ top: 20 }}
      >
        {projectDetail && (
          <div>
            {/* 核心信息概览 */}
            <Card title="核心信息概览" size="small" style={{ marginBottom: 16 }}>
              <Descriptions column={2} size="small">
                <Descriptions.Item label="项目名称">
                  {projectDetail.project.core_info.project_name}
                </Descriptions.Item>
                <Descriptions.Item label="项目编号">
                  {projectDetail.project.core_info.project_code || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="TMT编码">
                  {projectDetail.project.core_info.tmt_code || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="技术负责人">
                  {projectDetail.project.core_info.technical_lead}
                </Descriptions.Item>
                <Descriptions.Item label="阶段">
                  {projectDetail.project.core_info.stage || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="立项时间">
                  {projectDetail.project.core_info.project_start_date || '-'}
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* 完整项目数据 */}
            <Card title="完整项目数据" size="small">
              <Descriptions column={1} size="small" bordered>
                {Object.entries(projectDetail.project.complete_data).map(([key, value]) => (
                  <Descriptions.Item key={key} label={key}>
                    {value || '-'}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            </Card>

            <Divider />

            {/* 系统信息 */}
            <Card title="系统信息" size="small">
              <Descriptions column={2} size="small">
                <Descriptions.Item label="创建时间">
                  {new Date(projectDetail.project.system_info.created_at).toLocaleString('zh-CN')}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {new Date(projectDetail.project.system_info.updated_at).toLocaleString('zh-CN')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </div>
        )}
      </Modal>

      {/* 导入模态框 */}
      <Modal
        title="导入Excel文件"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          name="file"
          accept=".xlsx,.xls"
          beforeUpload={() => false}
          onChange={(info) => {
            if (info.file.status === 'done') {
              message.success('导入成功')
              setImportModalVisible(false)
              refetch()
            }
          }}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 .xlsx 和 .xls 格式的Excel文件
          </p>
        </Upload.Dragger>
      </Modal>
    </div>
  )
}

export default Tasks
