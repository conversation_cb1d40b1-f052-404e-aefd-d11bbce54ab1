"""
Excel处理服务
提供Excel文件导入解析和数据导出功能
"""

import os
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json

from ..models import Project, Task, User, ProjectPriority, ProjectStatus, ProjectStage
from ..utils import load_config, sanitize_filename, format_datetime


class ExcelService:
    """Excel处理服务类"""
    
    @staticmethod
    def get_excel_config() -> Dict[str, Any]:
        """获取Excel配置"""
        config = load_config()
        return config.get('excel', {
            'max_file_size_mb': 10,
            'allowed_extensions': ['.xlsx', '.xls'],
            'core_fields': [
                '序号', '项目名称', '项目编号', 'TMT编码', '技术负责人',
                '技术难度', '进度', '阶段', '立项时间', '完成时间'
            ],
            'caf_field_mapping': {
                # 基础信息字段
                '序号': 'sequence_number',
                '项目名称': 'project_name',
                '项目编号': 'project_code',
                'CAF编码': 'caf_code',
                'TMT编码': 'tmt_code',
                '中文名称': 'chinese_name',
                '英文名称': 'english_name',
                '技术负责人': 'technical_lead',
                '技术经理': 'technical_manager',
                '阶段': 'stage',
                '立项时间': 'project_start_date',
                '疲劳试验条件': 'fatigue_test_conditions',

                # 状态信息字段
                'CBC状态': 'cbc_status',
                '图纸编码与版本': 'drawing_code_version',
                '方案图纸状态\n（XX日提交/批准）': 'drawing_status',
                'TTP编码与版本': 'ttp_code_version',
                'TTP状态\n（XX日提交/批准）': 'ttp_status',
                'RTP编码与版本': 'rtp_code_version',
                'RTP状态\n（XX日提交/批准）': 'rtp_status',
                'TTR编码与版本': 'ttr_code_version',
                'TTR状态\n（XX日提交/批准）': 'ttr_status',

                # 时间和进度信息
                '内部图纸下发时间-技术工程师': 'internal_drawing_release_time',
                'FAI日期': 'fai_date',
                '说明': 'description',

                # 会议和讨论记录
                '10/2CAF现场讨论情况': 'caf_onsite_discussion_1002',
                '项目进展1': 'project_progress_1',
                '项目进展2': 'project_progress_2',
                '项目进展3': 'project_progress_3',
                '问题CAF现场交流-支持/讨论事项--2025-3-19': 'caf_issues_discussion_0319',
                '问题CAF会议-支持/讨论事项--2025-4-14': 'caf_meeting_issues_0414',
                '问题CAF会议-支持/讨论事项--2025-6-16': 'caf_meeting_issues_0616'
            },
            'field_mapping': {
                '项目名称': 'name',
                '项目编号': 'code',
                'CAF编码': 'caf_code',
                'TMT编码': 'tmt_code',
                '技术负责人': 'technical_lead',
                '技术难度': 'difficulty_level',
                '阶段': 'stage',
                '立项时间': 'start_date',
                '完成时间': 'end_date',
                '进度说明': 'progress_notes',
                '问题记录': 'issues',
                '会议纪要': 'meeting_notes'
            }
        })
    
    @classmethod
    def validate_excel_file(cls, file_path: str) -> Dict[str, Any]:
        """验证Excel文件"""
        try:
            config = cls.get_excel_config()
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {
                    'valid': False,
                    'message': '文件不存在',
                    'error_code': 'FILE_NOT_FOUND'
                }
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            max_size = config.get('max_file_size_mb', 10) * 1024 * 1024
            
            if file_size > max_size:
                return {
                    'valid': False,
                    'message': f'文件大小超过限制 ({config.get("max_file_size_mb", 10)}MB)',
                    'error_code': 'FILE_TOO_LARGE'
                }
            
            # 检查文件扩展名
            file_ext = os.path.splitext(file_path)[1].lower()
            allowed_extensions = config.get('allowed_extensions', ['.xlsx', '.xls'])
            
            if file_ext not in allowed_extensions:
                return {
                    'valid': False,
                    'message': f'不支持的文件格式，支持的格式: {", ".join(allowed_extensions)}',
                    'error_code': 'UNSUPPORTED_FORMAT'
                }
            
            # 尝试读取Excel文件
            try:
                df = pd.read_excel(file_path, sheet_name=0)
                if df.empty:
                    return {
                        'valid': False,
                        'message': 'Excel文件为空',
                        'error_code': 'EMPTY_FILE'
                    }
                
                return {
                    'valid': True,
                    'message': '文件验证通过',
                    'file_info': {
                        'size': file_size,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'sheet_names': pd.ExcelFile(file_path).sheet_names
                    }
                }
                
            except Exception as e:
                return {
                    'valid': False,
                    'message': f'无法读取Excel文件: {str(e)}',
                    'error_code': 'READ_ERROR'
                }
                
        except Exception as e:
            return {
                'valid': False,
                'message': f'文件验证失败: {str(e)}',
                'error_code': 'VALIDATION_ERROR'
            }
    
    @classmethod
    def analyze_excel_structure(cls, file_path: str, sheet_name: str = None) -> Dict[str, Any]:
        """分析Excel文件结构"""
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            
            if sheet_name is None:
                # 查找包含"CAF项目状态"的工作表
                target_sheet = None
                for sheet in excel_file.sheet_names:
                    if 'CAF' in sheet or '项目' in sheet or '状态' in sheet:
                        target_sheet = sheet
                        break
                
                if target_sheet is None:
                    target_sheet = excel_file.sheet_names[0]  # 使用第一个工作表
                
                sheet_name = target_sheet
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 分析列结构
            columns_info = []
            config = cls.get_excel_config()
            field_mapping = config.get('field_mapping', {})
            core_fields = config.get('core_fields', [])
            
            for col in df.columns:
                col_name = str(col).strip()
                
                # 检查数据类型和示例值
                non_null_values = df[col].dropna()
                sample_values = non_null_values.head(3).tolist() if len(non_null_values) > 0 else []
                
                column_info = {
                    'original_name': col_name,
                    'mapped_field': field_mapping.get(col_name, ''),
                    'is_core_field': col_name in core_fields,
                    'data_type': str(df[col].dtype),
                    'non_null_count': len(non_null_values),
                    'null_count': df[col].isnull().sum(),
                    'sample_values': sample_values,
                    'suggested_mapping': cls._suggest_field_mapping(col_name)
                }
                
                columns_info.append(column_info)
            
            return {
                'success': True,
                'analysis': {
                    'sheet_name': sheet_name,
                    'total_rows': len(df),
                    'total_columns': len(df.columns),
                    'columns': columns_info,
                    'available_sheets': excel_file.sheet_names,
                    'preview_data': df.head(5).to_dict('records')
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'分析Excel结构失败: {str(e)}',
                'error_code': 'ANALYSIS_ERROR'
            }
    
    @staticmethod
    def _suggest_field_mapping(column_name: str) -> str:
        """建议字段映射"""
        column_lower = column_name.lower()
        
        # 定义映射规则
        mapping_rules = {
            'name': ['名称', 'name', '项目名'],
            'code': ['编号', 'code', '项目编号'],
            'caf_code': ['caf', 'caf编码'],
            'tmt_code': ['tmt', 'tmt编码'],
            'technical_lead': ['负责人', '技术负责人', 'lead'],
            'difficulty_level': ['难度', '技术难度', 'difficulty'],
            'stage': ['阶段', 'stage', '状态'],
            'start_date': ['立项', '开始', 'start', '立项时间'],
            'end_date': ['完成', '结束', 'end', '完成时间'],
            'progress_notes': ['进度', 'progress', '进度说明'],
            'priority': ['优先级', 'priority', '重要性']
        }
        
        for field, keywords in mapping_rules.items():
            if any(keyword in column_lower for keyword in keywords):
                return field
        
        return ''
    
    @classmethod
    def import_caf_project_data(cls, file_path: str, sheet_name: str = None) -> Dict[str, Any]:
        """导入CAF项目数据"""
        try:
            # 验证文件
            validation_result = cls.validate_excel_file(file_path)
            if not validation_result['valid']:
                return validation_result

            # 读取数据
            if sheet_name is None:
                excel_file = pd.ExcelFile(file_path)
                # 优先查找CAF项目状态工作表
                target_sheet = None
                for sheet in excel_file.sheet_names:
                    if 'CAF项目状态' in sheet or 'CAF' in sheet or '项目状态' in sheet:
                        target_sheet = sheet
                        break
                sheet_name = target_sheet or excel_file.sheet_names[0]

            df = pd.read_excel(file_path, sheet_name=sheet_name)

            # 获取CAF字段映射
            config = cls.get_excel_config()
            caf_field_mapping = config.get('caf_field_mapping', {})

            # 如果配置中没有CAF字段映射，使用内置映射
            if not caf_field_mapping:
                caf_field_mapping = {
                    '序号': 'sequence_number',
                    '项目名称': 'project_name',
                    '项目编号': 'project_code',
                    'CAF编码': 'caf_code',
                    'TMT编码': 'tmt_code',
                    '中文名称': 'chinese_name',
                    '英文名称': 'english_name',
                    '技术负责人': 'technical_lead',
                    '技术经理': 'technical_manager',
                    '阶段': 'stage',
                    '立项时间': 'project_start_date',
                    '疲劳试验条件': 'fatigue_test_conditions',
                    'CBC状态': 'cbc_status',
                    '图纸编码与版本': 'drawing_code_version',
                    '方案图纸状态\n（XX日提交/批准）': 'drawing_status',
                    'TTP编码与版本': 'ttp_code_version',
                    'TTP状态\n（XX日提交/批准）': 'ttp_status',
                    'RTP编码与版本': 'rtp_code_version',
                    'RTP状态\n（XX日提交/批准）': 'rtp_status',
                    'TTR编码与版本': 'ttr_code_version',
                    'TTR状态\n（XX日提交/批准）': 'ttr_status',
                    '内部图纸下发时间-技术工程师': 'internal_drawing_release_time',
                    'FAI日期': 'fai_date',
                    '说明': 'description',
                    '10/2CAF现场讨论情况': 'caf_onsite_discussion_1002',
                    '项目进展1': 'project_progress_1',
                    '项目进展2': 'project_progress_2',
                    '项目进展3': 'project_progress_3',
                    '问题CAF现场交流-支持/讨论事项--2025-3-19': 'caf_issues_discussion_0319',
                    '问题CAF会议-支持/讨论事项--2025-4-14': 'caf_meeting_issues_0414',
                    '问题CAF会议-支持/讨论事项--2025-6-16': 'caf_meeting_issues_0616'
                }

            # 创建列名映射（处理Excel中的实际列名）
            actual_column_mapping = {}
            for excel_col, field_name in caf_field_mapping.items():
                # 查找匹配的实际列名
                for actual_col in df.columns:
                    # 标准化比较（去除空格和换行符）
                    normalized_excel = excel_col.replace('\n', '').replace(' ', '')
                    normalized_actual = str(actual_col).replace('\n', '').replace(' ', '')

                    if normalized_excel == normalized_actual or excel_col == actual_col:
                        actual_column_mapping[actual_col] = field_name
                        break

            # 处理数据
            imported_projects = []
            failed_rows = []

            for index, row in df.iterrows():
                try:
                    # 转换数据
                    project_data = {}

                    # 映射所有字段
                    for actual_col, field_name in actual_column_mapping.items():
                        value = row[actual_col]

                        # 处理空值
                        if pd.isna(value) or value == '' or str(value).strip() == '':
                            project_data[field_name] = None
                        else:
                            # 处理日期时间字段
                            if 'date' in field_name or 'time' in field_name:
                                if isinstance(value, datetime):
                                    project_data[field_name] = value.isoformat()
                                else:
                                    project_data[field_name] = str(value)
                            else:
                                project_data[field_name] = str(value).strip()

                    # 生成唯一ID
                    project_data['id'] = f"caf_{project_data.get('sequence_number', index + 1)}"

                    # 设置创建和更新时间
                    now = datetime.now().isoformat()
                    project_data['created_at'] = now
                    project_data['updated_at'] = now

                    imported_projects.append(project_data)

                except Exception as e:
                    failed_rows.append({
                        'row': index + 1,
                        'error': str(e),
                        'data': row.to_dict()
                    })

            return {
                'success': True,
                'message': f'成功导入 {len(imported_projects)} 个CAF项目',
                'imported_count': len(imported_projects),
                'failed_count': len(failed_rows),
                'projects': imported_projects,
                'failed_rows': failed_rows,
                'sheet_name': sheet_name
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'导入CAF项目数据失败: {str(e)}',
                'error_code': 'IMPORT_CAF_ERROR'
            }

    @classmethod
    def import_excel_data(cls, file_path: str, sheet_name: str = None,
                         field_mapping: Dict[str, str] = None,
                         import_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """导入Excel数据"""
        try:
            # 验证文件
            validation_result = cls.validate_excel_file(file_path)
            if not validation_result['valid']:
                return validation_result
            
            # 读取数据
            if sheet_name is None:
                excel_file = pd.ExcelFile(file_path)
                sheet_name = excel_file.sheet_names[0]
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 使用提供的字段映射或默认映射
            if field_mapping is None:
                config = cls.get_excel_config()
                field_mapping = config.get('field_mapping', {})
            
            # 导入选项
            options = import_options or {}
            skip_duplicates = options.get('skip_duplicates', True)
            update_existing = options.get('update_existing', False)
            
            # 处理数据
            imported_projects = []
            skipped_rows = []
            error_rows = []
            
            for index, row in df.iterrows():
                try:
                    project_data = cls._convert_row_to_project(row, field_mapping)
                    
                    if not project_data.get('name') or not project_data.get('code'):
                        skipped_rows.append({
                            'row': index + 1,
                            'reason': '缺少必要字段（项目名称或编号）',
                            'data': row.to_dict()
                        })
                        continue
                    
                    # 检查是否已存在
                    existing_project = Project.find_by(code=project_data['code'])
                    
                    if existing_project:
                        if skip_duplicates and not update_existing:
                            skipped_rows.append({
                                'row': index + 1,
                                'reason': f'项目编号 {project_data["code"]} 已存在',
                                'data': row.to_dict()
                            })
                            continue
                        elif update_existing:
                            # 更新现有项目
                            project = existing_project[0]
                            for key, value in project_data.items():
                                if value is not None and value != '':
                                    setattr(project, key, value)
                            project.save()
                            imported_projects.append(project.to_dict())
                            continue
                    
                    # 创建新项目
                    project = Project(**project_data)
                    project.save()
                    imported_projects.append(project.to_dict())
                    
                except Exception as e:
                    error_rows.append({
                        'row': index + 1,
                        'error': str(e),
                        'data': row.to_dict()
                    })
            
            return {
                'success': True,
                'message': f'成功导入 {len(imported_projects)} 个项目',
                'summary': {
                    'total_rows': len(df),
                    'imported': len(imported_projects),
                    'skipped': len(skipped_rows),
                    'errors': len(error_rows)
                },
                'imported_projects': imported_projects,
                'skipped_rows': skipped_rows,
                'error_rows': error_rows
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'导入Excel数据失败: {str(e)}',
                'error_code': 'IMPORT_ERROR'
            }
    
    @staticmethod
    def _convert_row_to_project(row: pd.Series, field_mapping: Dict[str, str]) -> Dict[str, Any]:
        """将Excel行数据转换为项目数据"""
        project_data = {}
        
        for excel_field, model_field in field_mapping.items():
            if excel_field in row.index and model_field:
                value = row[excel_field]
                
                # 处理空值
                if pd.isna(value) or value == '':
                    continue
                
                # 数据类型转换
                if model_field == 'difficulty_level':
                    try:
                        project_data[model_field] = int(float(value))
                    except (ValueError, TypeError):
                        project_data[model_field] = 3  # 默认难度
                
                elif model_field in ['start_date', 'end_date']:
                    try:
                        if isinstance(value, datetime):
                            project_data[model_field] = value.isoformat()
                        else:
                            # 尝试解析日期字符串
                            parsed_date = pd.to_datetime(value)
                            project_data[model_field] = parsed_date.isoformat()
                    except:
                        continue  # 跳过无效日期
                
                elif model_field == 'priority':
                    # 映射优先级
                    priority_mapping = {
                        '紧急': 'P0', '高': 'P1', '中': 'P2', '低': 'P3', '待定': 'P4'
                    }
                    project_data[model_field] = priority_mapping.get(str(value), 'P3')
                
                elif model_field == 'status':
                    # 映射状态
                    status_mapping = {
                        '规划中': 'planning', '进行中': 'in_progress',
                        '已完成': 'completed', '已暂停': 'paused', '已取消': 'cancelled'
                    }
                    project_data[model_field] = status_mapping.get(str(value), 'planning')
                
                elif model_field == 'stage':
                    # 映射阶段
                    stage_mapping = {
                        '立项': 'initiation', '设计': 'design', '开发': 'development',
                        '测试': 'testing', '部署': 'deployment', '维护': 'maintenance'
                    }
                    project_data[model_field] = stage_mapping.get(str(value), 'initiation')
                
                else:
                    project_data[model_field] = str(value).strip()
        
        # 设置默认值
        project_data.setdefault('priority', ProjectPriority.P3.value)
        project_data.setdefault('status', ProjectStatus.PLANNING.value)
        project_data.setdefault('stage', ProjectStage.INITIATION.value)
        project_data.setdefault('difficulty_level', 3)
        
        return project_data

    @classmethod
    def export_projects_to_excel(cls, projects: List[Project] = None,
                                export_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """导出项目数据到Excel"""
        try:
            if projects is None:
                projects = Project.find_all()

            if not projects:
                return {
                    'success': False,
                    'message': '没有可导出的项目数据',
                    'error_code': 'NO_DATA'
                }

            options = export_options or {}
            include_all_fields = options.get('include_all_fields', True)
            file_name = options.get('file_name', f'项目数据导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')

            # 准备导出数据
            export_data = []
            config = cls.get_excel_config()
            field_mapping = config.get('field_mapping', {})

            # 反向映射（模型字段 -> Excel字段）
            reverse_mapping = {v: k for k, v in field_mapping.items()}

            for project in projects:
                project_dict = project.to_dict()

                # 转换为Excel格式
                excel_row = {}

                if include_all_fields:
                    # 导出所有字段
                    for model_field, value in project_dict.items():
                        excel_field = reverse_mapping.get(model_field, model_field)
                        excel_row[excel_field] = cls._format_value_for_excel(model_field, value)
                else:
                    # 只导出核心字段
                    core_fields = config.get('core_fields', [])
                    for excel_field in core_fields:
                        model_field = field_mapping.get(excel_field, excel_field)
                        value = project_dict.get(model_field, '')
                        excel_row[excel_field] = cls._format_value_for_excel(model_field, value)

                export_data.append(excel_row)

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 确保导出目录存在
            export_dir = os.path.join(os.path.dirname(__file__), '../../exports')
            os.makedirs(export_dir, exist_ok=True)

            # 生成文件路径
            file_path = os.path.join(export_dir, sanitize_filename(file_name))

            # 导出到Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='CAF项目状态', index=False)

                # 设置列宽
                worksheet = writer.sheets['CAF项目状态']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            return {
                'success': True,
                'message': f'成功导出 {len(projects)} 个项目到Excel文件',
                'file_path': file_path,
                'file_name': file_name,
                'exported_count': len(projects)
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'导出Excel失败: {str(e)}',
                'error_code': 'EXPORT_ERROR'
            }

    @classmethod
    def export_caf_projects_to_excel(cls, projects: List = None,
                                    export_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """导出CAF项目数据到Excel"""
        try:
            if projects is None:
                from ..models import CAFProject
                projects = CAFProject.find_all()

            if not projects:
                return {
                    'success': False,
                    'message': '没有可导出的CAF项目数据',
                    'error_code': 'NO_DATA'
                }

            options = export_options or {}
            file_name = options.get('file_name', f'CAF项目数据导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')

            # 准备导出数据
            export_data = []
            config = cls.get_excel_config()
            caf_field_mapping = config.get('caf_field_mapping', {})

            # 创建反向映射（从字段名到Excel列名）
            reverse_mapping = {v: k for k, v in caf_field_mapping.items()}

            for project in projects:
                project_dict = project.to_dict()

                # 按Excel列顺序组织数据
                row_data = {}
                for field_name, excel_col in reverse_mapping.items():
                    value = project_dict.get(field_name, '')

                    # 处理空值
                    if value is None or value == '':
                        row_data[excel_col] = ''
                    else:
                        # 处理日期时间字段
                        if 'date' in field_name or 'time' in field_name:
                            if isinstance(value, str) and 'T' in value:
                                try:
                                    dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                                    row_data[excel_col] = dt
                                except:
                                    row_data[excel_col] = value
                            else:
                                row_data[excel_col] = value
                        else:
                            row_data[excel_col] = value

                export_data.append(row_data)

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 按原始Excel列顺序排列
            excel_columns = list(caf_field_mapping.keys())
            available_columns = [col for col in excel_columns if col in df.columns]
            df = df[available_columns]

            # 确保导出目录存在
            export_dir = os.path.join(os.path.dirname(__file__), '../../exports')
            os.makedirs(export_dir, exist_ok=True)

            # 生成文件路径
            file_path = os.path.join(export_dir, sanitize_filename(file_name))

            # 导出到Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='CAF项目状态', index=False)

                # 设置列宽
                worksheet = writer.sheets['CAF项目状态']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            return {
                'success': True,
                'message': f'成功导出 {len(projects)} 个CAF项目到Excel文件',
                'file_path': file_path,
                'file_name': file_name,
                'exported_count': len(projects)
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'导出CAF项目Excel失败: {str(e)}',
                'error_code': 'EXPORT_CAF_ERROR'
            }

    @classmethod
    def export_tasks_to_excel(cls, tasks: List[Task] = None,
                             export_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """导出任务数据到Excel"""
        try:
            if tasks is None:
                tasks = Task.find_all()

            if not tasks:
                return {
                    'success': False,
                    'message': '没有可导出的任务数据',
                    'error_code': 'NO_DATA'
                }

            options = export_options or {}
            file_name = options.get('file_name', f'任务数据导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')

            # 准备导出数据
            export_data = []

            for task in tasks:
                # 获取关联信息
                assignee = User.find_by_id(task.assignee_id) if task.assignee_id else None
                project = Project.find_by_id(task.project_id) if task.project_id else None
                creator = User.find_by_id(task.creator_id) if task.creator_id else None

                task_row = {
                    '任务ID': task.id,
                    '任务标题': task.title,
                    '任务描述': task.description,
                    '项目名称': project.name if project else '',
                    '项目编号': project.code if project else '',
                    '负责人': assignee.full_name if assignee else '',
                    '创建人': creator.full_name if creator else '',
                    '优先级': task.priority,
                    '状态': cls._translate_task_status(task.status),
                    '类型': cls._translate_task_type(task.type),
                    '难度等级': task.difficulty_level,
                    '预计工时': task.estimated_hours,
                    '实际工时': task.actual_hours,
                    '进度百分比': task.progress_percentage,
                    '开始日期': format_datetime(task.start_date, '%Y-%m-%d') if task.start_date else '',
                    '截止日期': format_datetime(task.due_date, '%Y-%m-%d') if task.due_date else '',
                    '完成日期': format_datetime(task.completed_at, '%Y-%m-%d') if task.completed_at else '',
                    '创建时间': format_datetime(task.created_at, '%Y-%m-%d %H:%M:%S'),
                    '是否自动分配': '是' if task.auto_assigned else '否',
                    '分配评分': task.assignment_score,
                    '分配原因': task.assignment_reason,
                    '是否超期': '是' if task.is_overdue() else '否',
                    '紧急程度评分': task.calculate_urgency_score(),
                    '所需技能': '、'.join(task.required_skills) if task.required_skills else '',
                    '标签': '、'.join(task.tags) if task.tags else '',
                    '进度说明': task.progress_notes
                }

                export_data.append(task_row)

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 确保导出目录存在
            export_dir = os.path.join(os.path.dirname(__file__), '../../exports')
            os.makedirs(export_dir, exist_ok=True)

            # 生成文件路径
            file_path = os.path.join(export_dir, sanitize_filename(file_name))

            # 导出到Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='任务列表', index=False)

                # 设置列宽
                worksheet = writer.sheets['任务列表']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            return {
                'success': True,
                'message': f'成功导出 {len(tasks)} 个任务到Excel文件',
                'file_path': file_path,
                'file_name': file_name,
                'exported_count': len(tasks)
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'导出任务Excel失败: {str(e)}',
                'error_code': 'EXPORT_TASKS_ERROR'
            }

    @staticmethod
    def _format_value_for_excel(field_name: str, value: Any) -> Any:
        """格式化值用于Excel导出"""
        if value is None or value == '':
            return ''

        # 日期时间格式化
        if field_name in ['start_date', 'end_date', 'created_at', 'updated_at']:
            return format_datetime(value, '%Y-%m-%d %H:%M:%S')

        # 优先级翻译
        if field_name == 'priority':
            priority_translation = {
                'P0': '紧急', 'P1': '高', 'P2': '中', 'P3': '低', 'P4': '待定', 'P5': '无关紧要'
            }
            return priority_translation.get(value, value)

        # 状态翻译
        if field_name == 'status':
            status_translation = {
                'planning': '规划中', 'in_progress': '进行中',
                'completed': '已完成', 'paused': '已暂停', 'cancelled': '已取消'
            }
            return status_translation.get(value, value)

        # 阶段翻译
        if field_name == 'stage':
            stage_translation = {
                'initiation': '立项', 'design': '设计', 'development': '开发',
                'testing': '测试', 'deployment': '部署', 'maintenance': '维护'
            }
            return stage_translation.get(value, value)

        # 列表转换为字符串
        if isinstance(value, list):
            return '、'.join(str(item) for item in value)

        return value

    @staticmethod
    def _translate_task_status(status: str) -> str:
        """翻译任务状态"""
        status_translation = {
            'pending': '待处理',
            'in_progress': '进行中',
            'review': '待审核',
            'completed': '已完成',
            'cancelled': '已取消',
            'blocked': '被阻塞'
        }
        return status_translation.get(status, status)

    @staticmethod
    def _translate_task_type(task_type: str) -> str:
        """翻译任务类型"""
        type_translation = {
            'design': '设计任务',
            'development': '开发任务',
            'testing': '测试任务',
            'review': '审核任务',
            'documentation': '文档任务',
            'meeting': '会议任务',
            'other': '其他任务'
        }
        return type_translation.get(task_type, task_type)

    @classmethod
    def get_export_templates(cls) -> Dict[str, Any]:
        """获取导出模板信息"""
        try:
            config = cls.get_excel_config()

            # 项目导出模板
            project_template = {
                'name': '项目数据模板',
                'description': '用于导出项目数据的Excel模板',
                'fields': [
                    {'excel_name': '序号', 'field_name': 'id', 'required': False},
                    {'excel_name': '项目名称', 'field_name': 'name', 'required': True},
                    {'excel_name': '项目编号', 'field_name': 'code', 'required': True},
                    {'excel_name': 'CAF编码', 'field_name': 'caf_code', 'required': False},
                    {'excel_name': 'TMT编码', 'field_name': 'tmt_code', 'required': False},
                    {'excel_name': '技术负责人', 'field_name': 'technical_lead', 'required': False},
                    {'excel_name': '技术难度', 'field_name': 'difficulty_level', 'required': False},
                    {'excel_name': '进度', 'field_name': 'progress_percentage', 'required': False},
                    {'excel_name': '阶段', 'field_name': 'stage', 'required': False},
                    {'excel_name': '立项时间', 'field_name': 'start_date', 'required': False},
                    {'excel_name': '完成时间', 'field_name': 'end_date', 'required': False}
                ]
            }

            # 任务导出模板
            task_template = {
                'name': '任务数据模板',
                'description': '用于导出任务数据的Excel模板',
                'fields': [
                    {'excel_name': '任务ID', 'field_name': 'id', 'required': False},
                    {'excel_name': '任务标题', 'field_name': 'title', 'required': True},
                    {'excel_name': '项目名称', 'field_name': 'project_name', 'required': False},
                    {'excel_name': '负责人', 'field_name': 'assignee_name', 'required': False},
                    {'excel_name': '优先级', 'field_name': 'priority', 'required': False},
                    {'excel_name': '状态', 'field_name': 'status', 'required': False},
                    {'excel_name': '预计工时', 'field_name': 'estimated_hours', 'required': False},
                    {'excel_name': '截止日期', 'field_name': 'due_date', 'required': False}
                ]
            }

            return {
                'success': True,
                'templates': {
                    'project': project_template,
                    'task': task_template
                },
                'config': config
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取导出模板失败: {str(e)}',
                'error_code': 'GET_TEMPLATES_ERROR'
            }
